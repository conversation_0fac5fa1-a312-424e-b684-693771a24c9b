"""
Comprehensive test suite for the modernized RAG system.
Tests all major components including Google embeddings, API endpoints, and error handling.
"""

import pytest
import asyncio
import os
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import numpy as np

# Import the components to test
from src.vector_db import VectorDatabase
from src.config import Config
from main import app, initialize_system, detect_missing_information_response, get_friendly_fallback_response

# Test client for FastAPI
from fastapi.testclient import TestClient

class TestGoogleEmbeddings:
    """Test Google embeddings integration."""
    
    @pytest.fixture
    def temp_db_path(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @patch('google.generativeai.embed_content')
    def test_vector_db_initialization(self, mock_embed, temp_db_path):
        """Test VectorDatabase initialization with Google embeddings."""
        # Mock the API key
        with patch.dict(os.environ, {'GOOGLE_API_KEY': 'test_key'}):
            vector_db = VectorDatabase(
                model_name="models/text-embedding-004",
                db_path=temp_db_path
            )
            
            assert vector_db.model_name == "models/text-embedding-004"
            assert vector_db.embedding_dim == 768
            assert vector_db.index is None
    
    @patch('google.generativeai.embed_content')
    def test_create_embeddings(self, mock_embed, temp_db_path):
        """Test embedding creation with Google API."""
        # Mock the embedding response
        mock_response = Mock()
        mock_response.embeddings = [
            Mock(embedding=[0.1] * 768),
            Mock(embedding=[0.2] * 768)
        ]
        mock_embed.return_value = mock_response
        
        with patch.dict(os.environ, {'GOOGLE_API_KEY': 'test_key'}):
            vector_db = VectorDatabase(
                model_name="models/text-embedding-004",
                db_path=temp_db_path
            )
            
            texts = ["Test text 1", "Test text 2"]
            embeddings = vector_db.create_embeddings(texts)
            
            assert embeddings.shape == (2, 768)
            assert mock_embed.called
    
    @patch('google.generativeai.embed_content')
    def test_query_embedding(self, mock_embed, temp_db_path):
        """Test query embedding creation."""
        # Mock the embedding response for query
        mock_response = Mock()
        mock_response.embedding = [0.1] * 768
        mock_embed.return_value = mock_response
        
        with patch.dict(os.environ, {'GOOGLE_API_KEY': 'test_key'}):
            vector_db = VectorDatabase(
                model_name="models/text-embedding-004",
                db_path=temp_db_path
            )
            
            query_embedding = vector_db._create_query_embedding("test query")
            
            assert len(query_embedding) == 768
            assert mock_embed.called


class TestAPIEndpoints:
    """Test FastAPI endpoints with new schema."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_chat_request_schema(self, client):
        """Test simplified chat request schema."""
        # Test valid request
        response = client.post("/chat", json={"query": "What is SKUAST?"})
        
        # Should not return 422 (validation error) for valid request
        assert response.status_code != 422
    
    def test_chat_request_validation(self, client):
        """Test request validation."""
        # Test empty query
        response = client.post("/chat", json={"query": ""})
        assert response.status_code == 422
        
        # Test missing query
        response = client.post("/chat", json={})
        assert response.status_code == 422
        
        # Test with old parameters (should be ignored)
        response = client.post("/chat", json={
            "query": "What is SKUAST?",
            "top_k": 5,  # Should be ignored
            "temperature": 0.7  # Should be ignored
        })
        # Should not fail due to extra parameters
        assert response.status_code != 422
    
    @patch('main.system_initialized', True)
    @patch('main.vector_db')
    @patch('main.retriever')
    @patch('main.gemini_client')
    def test_chat_response_schema(self, mock_gemini, mock_retriever, mock_vector_db, client):
        """Test simplified chat response schema."""
        # Mock the components
        mock_retriever.create_context_for_llm.return_value = {
            'context': 'Test context',
            'num_sources': 1,
            'sources': []
        }
        
        mock_gemini.generate_response.return_value = {
            'response': 'Test response',
            'success': True
        }
        
        response = client.post("/chat", json={"query": "What is SKUAST?"})
        
        if response.status_code == 200:
            data = response.json()
            # Should only contain 'response' field
            assert 'response' in data
            assert 'success' not in data
            assert 'sources' not in data
            assert 'metadata' not in data
            assert 'error' not in data


class TestMissingInformationDetection:
    """Test missing information detection and replacement."""
    
    def test_detect_missing_information_positive(self):
        """Test detection of missing information indicators."""
        test_cases = [
            "The document does not contain information about this topic.",
            "This is not mentioned in the document.",
            "No information about this subject is available.",
            "The document doesn't mention anything about this.",
            "Information is not available in the current document."
        ]
        
        for text in test_cases:
            assert detect_missing_information_response(text) == True
    
    def test_detect_missing_information_negative(self):
        """Test that normal responses are not flagged."""
        test_cases = [
            "SKUAST is a university focused on agricultural sciences.",
            "The university offers various programs in agriculture.",
            "Here is the information you requested about SKUAST."
        ]
        
        for text in test_cases:
            assert detect_missing_information_response(text) == False
    
    def test_friendly_fallback_response(self):
        """Test friendly fallback response generation."""
        response = get_friendly_fallback_response()
        
        assert isinstance(response, str)
        assert len(response) > 0
        assert "sorry" in response.lower() or "don't have" in response.lower()


class TestErrorHandling:
    """Test comprehensive error handling."""
    
    @pytest.fixture
    def client(self):
        """Create test client."""
        return TestClient(app)
    
    def test_system_not_initialized(self, client):
        """Test behavior when system is not initialized."""
        with patch('main.system_initialized', False):
            response = client.post("/chat", json={"query": "test"})
            assert response.status_code == 503
    
    def test_missing_components(self, client):
        """Test behavior when system components are missing."""
        with patch('main.system_initialized', True):
            with patch('main.vector_db', None):
                response = client.post("/chat", json={"query": "test"})
                assert response.status_code == 503
    
    @patch('main.system_initialized', True)
    @patch('main.vector_db')
    @patch('main.retriever')
    @patch('main.gemini_client')
    def test_retriever_error_handling(self, mock_gemini, mock_retriever, mock_vector_db, client):
        """Test error handling in retriever."""
        # Mock retriever to raise an exception
        mock_retriever.create_context_for_llm.side_effect = Exception("Retriever error")
        
        response = client.post("/chat", json={"query": "test"})
        assert response.status_code == 500
    
    @patch('main.system_initialized', True)
    @patch('main.vector_db')
    @patch('main.retriever')
    @patch('main.gemini_client')
    def test_gemini_error_handling(self, mock_gemini, mock_retriever, mock_vector_db, client):
        """Test error handling in Gemini client."""
        # Mock successful retriever
        mock_retriever.create_context_for_llm.return_value = {
            'context': 'Test context',
            'num_sources': 1
        }
        
        # Mock Gemini to return failure
        mock_gemini.generate_response.return_value = {
            'response': 'Error occurred',
            'success': False,
            'error': 'API error'
        }
        
        response = client.post("/chat", json={"query": "test"})
        
        if response.status_code == 200:
            data = response.json()
            assert 'response' in data
            # Should return user-friendly error message
            assert "sorry" in data['response'].lower() or "couldn't" in data['response'].lower()


class TestConfiguration:
    """Test configuration changes."""
    
    def test_default_embedding_model(self):
        """Test that Google embeddings are used by default."""
        assert Config.EMBEDDING_MODEL == "models/text-embedding-004"
        assert Config.EMBEDDING_DIMENSION == 768
    
    def test_legacy_model_config(self):
        """Test that legacy model config is available."""
        assert hasattr(Config, 'LEGACY_EMBEDDING_MODEL')
        assert Config.LEGACY_EMBEDDING_MODEL == "all-MiniLM-L6-v2"
        assert Config.LEGACY_EMBEDDING_DIMENSION == 384


# Integration test
@pytest.mark.asyncio
async def test_system_initialization():
    """Test system initialization with mocked components."""
    with patch.dict(os.environ, {'GOOGLE_API_KEY': 'test_key'}):
        with patch('src.vector_db.VectorDatabase') as mock_vector_db:
            with patch('src.retriever.SemanticRetriever') as mock_retriever:
                with patch('src.gemini_client.GeminiClient') as mock_gemini:
                    # Mock successful initialization
                    mock_vector_db.return_value.load_index.return_value = True
                    mock_gemini.return_value.validate_api_key.return_value = True
                    
                    # Should not raise an exception
                    await initialize_system()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
