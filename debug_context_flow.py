#!/usr/bin/env python3
"""
Debug script to trace the complete context flow from chunking to LLM input.
This script will show exactly what content is passed at each stage.
"""

import sys
import os
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pdf_processor import PDFProcessor
from text_chunker import TextChunker
from vector_db import VectorDatabase
from retriever import SemanticRetriever
from gemini_client import GeminiClient
from config import Config


def debug_context_flow():
    """Debug the complete context flow from PDF to LLM."""
    
    print("🔍 DEBUGGING SKUAST RAG CONTEXT FLOW")
    print("=" * 60)
    
    # Step 1: Check if PDF exists and process it
    pdf_path = "skuast.pdf"
    if not Path(pdf_path).exists():
        print("❌ PDF file not found. Please ensure skuast.pdf is in the project root.")
        return
    
    print(f"✅ Found PDF: {pdf_path}")
    
    # Step 2: Process PDF (if needed)
    processor = PDFProcessor()
    print("\n📄 STEP 1: PDF PROCESSING")
    print("-" * 30)
    
    try:
        markdown_content = processor.process_pdf(pdf_path)
        print(f"✅ PDF processed successfully")
        print(f"📊 Total content length: {len(markdown_content):,} characters")
        print(f"📝 Content preview (first 200 chars):")
        print(f"   {repr(markdown_content[:200])}")
        print(f"📝 Content preview (last 200 chars):")
        print(f"   {repr(markdown_content[-200:])}")
    except Exception as e:
        print(f"❌ PDF processing failed: {e}")
        return
    
    # Step 3: Text Chunking
    print("\n📝 STEP 2: TEXT CHUNKING")
    print("-" * 30)
    
    chunking_config = Config.get_chunking_config()
    chunker = TextChunker(
        chunk_size=chunking_config['chunk_size'],
        overlap_size=chunking_config['overlap_size']
    )
    
    chunks = chunker.split_text_into_chunks(markdown_content)
    print(f"✅ Text chunked successfully")
    print(f"📊 Total chunks created: {len(chunks)}")
    print(f"📊 Chunk size setting: {chunking_config['chunk_size']} characters")
    print(f"📊 Overlap size setting: {chunking_config['overlap_size']} characters")
    
    # Show chunk size distribution
    chunk_lengths = [chunk['length'] for chunk in chunks]
    print(f"📊 Chunk lengths - Min: {min(chunk_lengths)}, Max: {max(chunk_lengths)}, Avg: {sum(chunk_lengths)/len(chunk_lengths):.0f}")
    
    # Show first few chunks in detail
    print(f"\n📋 FIRST 3 CHUNKS DETAILS:")
    for i, chunk in enumerate(chunks[:3]):
        print(f"   Chunk {i+1}:")
        print(f"     ID: {chunk['id']}")
        print(f"     Length: {chunk['length']} characters")
        print(f"     Token count: {chunk['token_count']}")
        print(f"     First 100 chars: {repr(chunk['text'][:100])}")
        print(f"     Last 100 chars: {repr(chunk['text'][-100:])}")
        print()
    
    # Step 4: Vector Database
    print("\n🔍 STEP 3: VECTOR DATABASE CREATION")
    print("-" * 30)
    
    vector_db_config = Config.get_vector_db_config()
    vector_db = VectorDatabase(
        model_name=vector_db_config['model_name'],
        db_path=vector_db_config['db_path']
    )
    
    # Build index
    build_success = vector_db.build_index(chunks)
    if build_success:
        print(f"✅ Vector index built successfully")
        print(f"📊 Index stats: {vector_db.get_index_stats()}")
    else:
        print(f"❌ Vector index build failed")
        return
    
    # Step 5: Semantic Retrieval
    print("\n🎯 STEP 4: SEMANTIC RETRIEVAL")
    print("-" * 30)
    
    retriever = SemanticRetriever(vector_db)
    
    # Test with a sample query
    test_query = "What programs and courses does SKUAST offer?"
    print(f"🔍 Test query: '{test_query}'")
    
    # Get retrieved chunks
    retrieved_chunks = retriever.retrieve_relevant_chunks(test_query, top_k=3, include_metadata=True)
    print(f"✅ Retrieved {len(retrieved_chunks)} chunks")
    
    print(f"\n📋 RETRIEVED CHUNKS DETAILS:")
    for i, chunk in enumerate(retrieved_chunks):
        print(f"   Retrieved Chunk {i+1}:")
        print(f"     Chunk ID: {chunk['chunk_id']}")
        print(f"     Relevance Score: {chunk['relevance_score']:.4f}")
        print(f"     Full Text Length: {len(chunk['text'])} characters")
        print(f"     Text Preview Length: {len(chunk.get('text_preview', ''))} characters")
        print(f"     Text Preview: {repr(chunk.get('text_preview', ''))}")
        print(f"     Full Text (first 200 chars): {repr(chunk['text'][:200])}")
        print(f"     Full Text (last 200 chars): {repr(chunk['text'][-200:])}")
        print()
    
    # Step 6: Context Assembly for LLM
    print("\n🤖 STEP 5: CONTEXT ASSEMBLY FOR LLM")
    print("-" * 30)
    
    context_data = retriever.create_context_for_llm(test_query, top_k=3)
    
    print(f"✅ Context assembled for LLM")
    print(f"📊 Number of sources: {context_data['num_sources']}")
    print(f"📊 Average relevance score: {context_data.get('avg_relevance_score', 0):.4f}")
    print(f"📊 Full context length: {len(context_data['context'])} characters")
    
    print(f"\n📋 CONTEXT SENT TO LLM:")
    print(f"   Context length: {len(context_data['context'])} characters")
    print(f"   Context preview (first 500 chars):")
    print(f"   {repr(context_data['context'][:500])}")
    print(f"   Context preview (last 500 chars):")
    print(f"   {repr(context_data['context'][-500:])}")
    
    print(f"\n📋 SOURCES FOR API RESPONSE:")
    for source in context_data['sources']:
        print(f"   Source {source['source_id']}:")
        print(f"     Chunk ID: {source['chunk_id']}")
        print(f"     Relevance Score: {source['relevance_score']:.4f}")
        print(f"     Text Preview: {repr(source['text_preview'])}")
        print()
    
    # Step 7: Gemini Processing (if API key available)
    print("\n🤖 STEP 6: GEMINI LLM PROCESSING")
    print("-" * 30)
    
    if not Config.GOOGLE_API_KEY:
        print("⚠️  Google API key not configured - skipping LLM processing")
        print("   Set GOOGLE_API_KEY environment variable to test LLM processing")
    else:
        try:
            gemini_client = GeminiClient(api_key=Config.GOOGLE_API_KEY, model_name=Config.GEMINI_MODEL)
            
            print(f"✅ Gemini client initialized")
            print(f"📊 Model: {Config.GEMINI_MODEL}")
            
            # Generate response
            gemini_response = gemini_client.generate_response(
                query=test_query,
                context=context_data['context'],
                temperature=0.3
            )
            
            if gemini_response['success']:
                print(f"✅ Response generated successfully")
                print(f"📊 Response length: {len(gemini_response['response'])} characters")
                print(f"📊 Metadata: {gemini_response.get('metadata', {})}")
                print(f"📝 Response preview (first 300 chars):")
                print(f"   {repr(gemini_response['response'][:300])}")
            else:
                print(f"❌ Response generation failed: {gemini_response.get('error', 'Unknown error')}")
                
        except Exception as e:
            print(f"❌ Gemini processing failed: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 CONTEXT FLOW SUMMARY")
    print("=" * 60)
    
    print(f"1. PDF Content: {len(markdown_content):,} characters")
    print(f"2. Text Chunks: {len(chunks)} chunks (avg {sum(chunk_lengths)/len(chunk_lengths):.0f} chars each)")
    print(f"3. Retrieved Chunks: {len(retrieved_chunks)} chunks")
    print(f"4. LLM Context: {len(context_data['context'])} characters")
    print(f"5. API Response Sources: {len(context_data['sources'])} sources with previews")
    
    print(f"\n🔍 KEY FINDINGS:")
    print(f"✅ Full chunk content ({chunking_config['chunk_size']} chars max) is sent to LLM")
    print(f"✅ Text previews (150 chars max) are only for API response display")
    print(f"✅ Context truncation limit: {Config.MAX_CONTEXT_LENGTH} characters")
    print(f"✅ Actual context length: {len(context_data['context'])} characters")
    
    if len(context_data['context']) > Config.MAX_CONTEXT_LENGTH:
        print(f"⚠️  Context would be truncated before sending to LLM")
    else:
        print(f"✅ Context is within limits - no truncation needed")
    
    print(f"\n🎯 VERIFICATION COMPLETE")
    print(f"The system correctly sends full chunk content to the LLM while")
    print(f"providing abbreviated previews in the API response for UI purposes.")


if __name__ == "__main__":
    debug_context_flow()
