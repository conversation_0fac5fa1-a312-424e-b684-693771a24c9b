"""
FAISS vector database module for semantic search using Google embeddings.
"""

import os
import pickle
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import faiss
import google.generativeai as genai
import time
from concurrent.futures import ThreadPoolExecutor, as_completed

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VectorDatabase:
    """
    A class to handle FAISS vector database operations for semantic search using Google embeddings.
    """

    def __init__(self, model_name: str = "models/text-embedding-004", db_path: str = "data/vector_db", api_key: str = None):
        """
        Initialize the vector database with Google embeddings.

        Args:
            model_name (str): Name of the Google embedding model
            db_path (str): Path to store the vector database
            api_key (str): Google API key (if None, will try to get from environment)
        """
        self.model_name = model_name
        self.db_path = Path(db_path)
        self.db_path.mkdir(parents=True, exist_ok=True)

        # Initialize Google API
        if api_key is None:
            api_key = os.getenv('GOOGLE_API_KEY')

        if not api_key:
            raise ValueError("Google API key not provided. Set GOOGLE_API_KEY environment variable or pass api_key parameter.")

        genai.configure(api_key=api_key)

        # Set embedding dimension based on model
        if "text-embedding-004" in model_name:
            self.embedding_dim = 768
        elif "text-embedding-003" in model_name:
            self.embedding_dim = 768
        else:
            # Default dimension, will be updated after first embedding call
            self.embedding_dim = 768

        # Initialize FAISS index
        self.index = None
        self.chunks_metadata = []

        # Rate limiting parameters
        self.max_requests_per_minute = 1500  # Google's limit
        self.request_delay = 60.0 / self.max_requests_per_minute  # Delay between requests

        logger.info(f"VectorDatabase initialized with Google embedding model: {model_name}")
        logger.info(f"Embedding dimension: {self.embedding_dim}")
    
    def create_embeddings(self, texts: List[str], batch_size: int = 100) -> np.ndarray:
        """
        Create embeddings for a list of texts using Google's embedding API.

        Args:
            texts (List[str]): List of text strings
            batch_size (int): Batch size for processing (Google API supports up to 100)

        Returns:
            np.ndarray: Array of embeddings
        """
        if not texts:
            return np.array([])

        logger.info(f"Creating embeddings for {len(texts)} texts using Google API")

        try:
            all_embeddings = []

            # Process texts in batches to respect API limits
            for i in range(0, len(texts), batch_size):
                batch_texts = texts[i:i + batch_size]
                logger.info(f"Processing batch {i//batch_size + 1}/{(len(texts) + batch_size - 1)//batch_size}")

                # Add rate limiting delay
                if i > 0:
                    time.sleep(self.request_delay)

                batch_embeddings = self._create_batch_embeddings(batch_texts)
                all_embeddings.extend(batch_embeddings)

            # Convert to numpy array
            embeddings = np.array(all_embeddings, dtype=np.float32)

            # Normalize embeddings for cosine similarity
            norms = np.linalg.norm(embeddings, axis=1, keepdims=True)
            embeddings = embeddings / np.maximum(norms, 1e-8)  # Avoid division by zero

            logger.info(f"Successfully created embeddings with shape: {embeddings.shape}")
            return embeddings

        except Exception as e:
            logger.error(f"Error creating embeddings: {e}")
            raise

    def _create_batch_embeddings(self, texts: List[str], max_retries: int = 3) -> List[List[float]]:
        """
        Create embeddings for a batch of texts with retry logic.

        Args:
            texts (List[str]): List of text strings
            max_retries (int): Maximum number of retries

        Returns:
            List[List[float]]: List of embedding vectors
        """
        for attempt in range(max_retries):
            try:
                # Process texts one by one for better compatibility
                embeddings = []

                for text in texts:
                    # Use Google's embedding API for single text
                    result = genai.embed_content(
                        model=self.model_name,
                        content=text,
                        task_type="RETRIEVAL_DOCUMENT"
                    )

                    # Extract embedding - Google API returns a dictionary
                    if isinstance(result, dict) and 'embedding' in result:
                        embeddings.append(result['embedding'])
                    else:
                        logger.error(f"Unexpected result structure: {type(result)}, keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                        raise ValueError(f"No embedding found in result for text: {text[:50]}...")

                if not embeddings:
                    raise ValueError("No embeddings were created")

                logger.info(f"Successfully created {len(embeddings)} embeddings")
                return embeddings

            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * self.request_delay  # Exponential backoff
                    logger.warning(f"Embedding attempt {attempt + 1} failed: {e}. Retrying in {wait_time:.2f}s...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Failed to create embeddings after {max_retries} attempts: {e}")
                    raise
    
    def build_index(self, chunks: List[Dict[str, Any]], status_callback=None) -> bool:
        """
        Build FAISS index from text chunks.

        Args:
            chunks (List[Dict[str, Any]]): List of chunk dictionaries
            status_callback: Optional callback function for status updates

        Returns:
            bool: True if successful, False otherwise
        """
        if not chunks:
            msg = "No chunks provided for index building"
            logger.warning(msg)
            if status_callback:
                status_callback(msg, "warning")
            return False

        msg = f"Building FAISS index for {len(chunks)} chunks"
        logger.info(msg)
        if status_callback:
            status_callback(msg, "info")

        # Extract texts from chunks and validate
        valid_chunks = []
        texts = []

        for i, chunk in enumerate(chunks):
            if not chunk.get('text') or not chunk['text'].strip():
                warn_msg = f"Skipping chunk {i} due to empty text content"
                logger.warning(warn_msg)
                if status_callback:
                    status_callback(warn_msg, "warning")
                continue

            valid_chunks.append(chunk)
            texts.append(chunk['text'])

        if not texts:
            msg = "No valid text content found in chunks"
            logger.error(msg)
            if status_callback:
                status_callback(msg, "error")
            return False

        # Create embeddings
        try:
            embeddings = self.create_embeddings(texts)

            if embeddings.size == 0:
                msg = "No embeddings created - all texts failed to embed"
                logger.error(msg)
                if status_callback:
                    status_callback(msg, "error")
                return False

            # Validate embeddings shape
            if embeddings.ndim != 2 or embeddings.shape[1] != self.embedding_dim:
                msg = f"Embeddings shape error. Expected (N, {self.embedding_dim}), got {embeddings.shape}"
                logger.error(msg)
                if status_callback:
                    status_callback(msg, "error")
                return False

            # Create FAISS index
            # Using IndexFlatIP for cosine similarity (since embeddings are normalized)
            self.index = faiss.IndexFlatIP(self.embedding_dim)

            # Add embeddings to index
            self.index.add(embeddings.astype(np.float32))

            # Store metadata for valid chunks only
            self.chunks_metadata = valid_chunks.copy()

            success_msg = f"FAISS index built successfully with {self.index.ntotal} vectors"
            logger.info(success_msg)
            if status_callback:
                status_callback(success_msg, "success")

            return True

        except Exception as e:
            error_msg = f"Error building FAISS index: {e}"
            logger.error(error_msg)
            if status_callback:
                status_callback(error_msg, "error")
            return False
    
    def save_index(self, index_name: str = "skuast_index") -> None:
        """
        Save the FAISS index and metadata to disk.
        
        Args:
            index_name (str): Name for the index files
        """
        if self.index is None:
            logger.error("No index to save")
            return
        
        try:
            # Save FAISS index
            index_path = self.db_path / f"{index_name}.faiss"
            faiss.write_index(self.index, str(index_path))
            
            # Save metadata
            metadata_path = self.db_path / f"{index_name}_metadata.pkl"
            with open(metadata_path, 'wb') as f:
                pickle.dump(self.chunks_metadata, f)
            
            # Save model info
            model_info = {
                'model_name': self.model_name,
                'embedding_dim': self.embedding_dim,
                'num_vectors': self.index.ntotal
            }
            info_path = self.db_path / f"{index_name}_info.pkl"
            with open(info_path, 'wb') as f:
                pickle.dump(model_info, f)
            
            logger.info(f"Index saved successfully to {self.db_path}")
            
        except Exception as e:
            logger.error(f"Error saving index: {e}")
            raise
    
    def load_index(self, index_name: str = "skuast_index") -> bool:
        """
        Load the FAISS index and metadata from disk.
        
        Args:
            index_name (str): Name of the index files
            
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            # Check if files exist
            index_path = self.db_path / f"{index_name}.faiss"
            metadata_path = self.db_path / f"{index_name}_metadata.pkl"
            info_path = self.db_path / f"{index_name}_info.pkl"
            
            if not all(path.exists() for path in [index_path, metadata_path, info_path]):
                logger.warning(f"Index files not found in {self.db_path}")
                return False
            
            # Load model info and verify compatibility
            with open(info_path, 'rb') as f:
                model_info = pickle.load(f)
            
            if model_info['model_name'] != self.model_name:
                logger.warning(f"Model mismatch: expected {self.model_name}, found {model_info['model_name']}")
                return False
            
            # Load FAISS index
            self.index = faiss.read_index(str(index_path))
            
            # Load metadata
            with open(metadata_path, 'rb') as f:
                self.chunks_metadata = pickle.load(f)
            
            logger.info(f"Index loaded successfully: {self.index.ntotal} vectors")
            return True
            
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            return False
    
    def search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Search for similar chunks using the query.

        Args:
            query (str): Search query
            top_k (int): Number of top results to return

        Returns:
            List[Dict[str, Any]]: List of search results with scores
        """
        if self.index is None or self.index.ntotal == 0:
            logger.error("No index loaded or index is empty")
            return []

        if not query.strip():
            logger.warning("Empty query provided")
            return []

        try:
            # Create embedding for query using Google API
            query_embeddings = self._create_query_embedding(query)

            if len(query_embeddings) == 0:
                logger.error("Failed to create query embedding")
                return []

            # Convert to numpy array and normalize
            query_embedding = np.array([query_embeddings], dtype=np.float32)
            norms = np.linalg.norm(query_embedding, axis=1, keepdims=True)
            query_embedding = query_embedding / np.maximum(norms, 1e-8)

            # Adjust top_k to available vectors
            actual_k = min(top_k, self.index.ntotal)
            if actual_k == 0:
                logger.warning("No vectors available for search")
                return []

            # Search in FAISS index
            scores, indices = self.index.search(query_embedding, actual_k)

            # Validate search results
            if indices.size == 0 or (indices.ndim > 1 and len(indices[0]) > 0 and indices[0][0] == -1):
                logger.info("No relevant documents found")
                return []

            # Prepare results with bounds checking
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # Invalid index
                    continue

                if 0 <= idx < len(self.chunks_metadata):
                    result = {
                        'rank': i + 1,
                        'score': float(score),
                        'chunk_id': self.chunks_metadata[idx]['id'],
                        'text': self.chunks_metadata[idx]['text'],
                        'metadata': {
                            'length': self.chunks_metadata[idx]['length'],
                            'chunk_index': self.chunks_metadata[idx]['chunk_index'],
                            'token_count': self.chunks_metadata[idx].get('token_count', 0)
                        }
                    }
                    results.append(result)
                else:
                    logger.warning(f"Index {idx} from FAISS search is out of bounds for chunks_metadata (size {len(self.chunks_metadata)})")

            logger.info(f"Search completed: found {len(results)} results for query: '{query[:50]}...'")
            return results

        except Exception as e:
            logger.error(f"Error during search: {e}")
            return []

    def _create_query_embedding(self, query: str, max_retries: int = 3) -> List[float]:
        """
        Create embedding for a single query with retry logic.

        Args:
            query (str): Query text
            max_retries (int): Maximum number of retries

        Returns:
            List[float]: Query embedding vector
        """
        for attempt in range(max_retries):
            try:
                # Use Google's embedding API for query
                result = genai.embed_content(
                    model=self.model_name,
                    content=query,
                    task_type="RETRIEVAL_QUERY"
                )

                # Extract embedding - Google API returns a dictionary
                if isinstance(result, dict) and 'embedding' in result:
                    return result['embedding']
                else:
                    logger.error(f"Unexpected query result structure: {type(result)}, keys: {list(result.keys()) if isinstance(result, dict) else 'Not a dict'}")
                    raise ValueError("No embedding returned from Google API")

            except Exception as e:
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * self.request_delay  # Exponential backoff
                    logger.warning(f"Query embedding attempt {attempt + 1} failed: {e}. Retrying in {wait_time:.2f}s...")
                    time.sleep(wait_time)
                else:
                    logger.error(f"Failed to create query embedding after {max_retries} attempts: {e}")
                    raise
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the current index.
        
        Returns:
            Dict[str, Any]: Index statistics
        """
        if self.index is None:
            return {'status': 'No index loaded'}
        
        stats = {
            'status': 'Index loaded',
            'model_name': self.model_name,
            'embedding_dimension': self.embedding_dim,
            'total_vectors': self.index.ntotal,
            'total_chunks': len(self.chunks_metadata),
            'index_type': type(self.index).__name__
        }
        
        if self.chunks_metadata:
            total_chars = sum(chunk['length'] for chunk in self.chunks_metadata)
            total_tokens = sum(chunk.get('token_count', 0) for chunk in self.chunks_metadata)
            
            stats.update({
                'total_characters': total_chars,
                'total_tokens': total_tokens,
                'avg_chunk_length': total_chars / len(self.chunks_metadata),
                'avg_tokens_per_chunk': total_tokens / len(self.chunks_metadata) if total_tokens > 0 else 0
            })
        
        return stats
    
    def add_chunks(self, new_chunks: List[Dict[str, Any]]) -> None:
        """
        Add new chunks to existing index.
        
        Args:
            new_chunks (List[Dict[str, Any]]): List of new chunk dictionaries
        """
        if not new_chunks:
            logger.warning("No new chunks to add")
            return
        
        logger.info(f"Adding {len(new_chunks)} new chunks to index")
        
        # Create embeddings for new chunks
        texts = [chunk['text'] for chunk in new_chunks]
        embeddings = self.create_embeddings(texts)
        
        if self.index is None:
            # Create new index if none exists
            self.index = faiss.IndexFlatIP(self.embedding_dim)
            self.chunks_metadata = []
        
        # Add to index
        self.index.add(embeddings.astype(np.float32))
        
        # Update metadata
        self.chunks_metadata.extend(new_chunks)
        
        logger.info(f"Successfully added chunks. Total vectors: {self.index.ntotal}")


def main():
    """
    Main function for testing the vector database with Google embeddings.
    """
    try:
        # Initialize vector database with Google embeddings
        vector_db = VectorDatabase(
            model_name="models/text-embedding-004",
            db_path="data/test_vector_db"
        )

        # Sample chunks for testing
        sample_chunks = [
            {
                'id': 'chunk_1',
                'text': 'This is about agriculture and farming techniques in Karnataka.',
                'length': 65,
                'chunk_index': 0,
                'token_count': 12
            },
            {
                'id': 'chunk_2',
                'text': 'SKUAST focuses on agricultural research and education.',
                'length': 54,
                'chunk_index': 1,
                'token_count': 10
            },
            {
                'id': 'chunk_3',
                'text': 'The university offers various programs in agricultural sciences.',
                'length': 63,
                'chunk_index': 2,
                'token_count': 11
            }
        ]

        # Build index
        print("Building index with Google embeddings...")
        success = vector_db.build_index(sample_chunks)

        if not success:
            print("Failed to build index")
            return

        # Save index
        vector_db.save_index("test_index")
        print("Index saved successfully")

        # Test search
        print("\nTesting search...")
        results = vector_db.search("agricultural research", top_k=2)

        print("Search Results:")
        for result in results:
            print(f"Rank {result['rank']}: Score {result['score']:.4f}")
            print(f"Text: {result['text']}")
            print()

        # Show stats
        stats = vector_db.get_index_stats()
        print("Index Statistics:")
        for key, value in stats.items():
            print(f"{key}: {value}")

    except Exception as e:
        print(f"Error in main: {e}")
        logger.error(f"Error in main: {e}")


if __name__ == "__main__":
    main()
