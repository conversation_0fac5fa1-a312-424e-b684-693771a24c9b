"""
SKUAST RAG Chatbot - FastAPI Application
"""

import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from contextlib import asynccontextmanager

from fastapi import FastAP<PERSON>, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from src.pdf_processor import PDFProcessor
from src.text_chunker import TextChunker
from src.vector_db import VectorDatabase
from src.retriever import SemanticRetriever
from src.gemini_client import GeminiClient
from src.config import Config

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for components
vector_db: Optional[VectorDatabase] = None
retriever: Optional[SemanticRetriever] = None
gemini_client: Optional[GeminiClient] = None
system_initialized = False


# Pydantic models
class ChatRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="User query about SKUAST")


class ChatResponse(BaseModel):
    response: str = Field(..., description="AI-generated response")


class SystemStatus(BaseModel):
    status: str = Field(..., description="Overall system status")
    components: Dict[str, Any] = Field(..., description="Status of individual components")
    statistics: Dict[str, Any] = Field(default={}, description="System statistics")


async def initialize_system():
    """Initialize all system components with comprehensive error handling."""
    global vector_db, retriever, gemini_client, system_initialized

    try:
        logger.info("Initializing SKUAST RAG system...")

        # Validate configuration first with error handling
        try:
            config_validation = Config.validate_config()
            if not config_validation['valid']:
                logger.error("Configuration validation failed:")
                for issue in config_validation['issues']:
                    logger.error(f"  - {issue}")
                raise ValueError("Invalid configuration")

            # Log warnings
            for warning in config_validation['warnings']:
                logger.warning(warning)
        except Exception as e:
            logger.error(f"Error during configuration validation: {e}")
            raise ValueError(f"Configuration validation error: {e}")

        # Initialize vector database with error handling
        try:
            logger.info("Initializing vector database...")
            vector_db_config = Config.get_vector_db_config()

            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            vector_db = VectorDatabase(
                model_name=vector_db_config['model_name'],
                db_path=vector_db_config['db_path'],
                api_key=Config.GOOGLE_API_KEY
            )
        except Exception as e:
            logger.error(f"Error initializing vector database: {e}")
            raise ValueError(f"Vector database initialization failed: {e}")

        # Try to load existing index with error handling
        try:
            if vector_db.load_index(Config.INDEX_NAME):
                logger.info("Loaded existing FAISS index")
            else:
                logger.info("No existing index found. Will need to process PDF first.")
        except Exception as e:
            logger.warning(f"Error loading existing index: {e}")
            logger.info("Will need to process PDF to create new index.")

        # Initialize retriever with error handling
        try:
            logger.info("Initializing semantic retriever...")
            retriever = SemanticRetriever(vector_db)
        except Exception as e:
            logger.error(f"Error initializing semantic retriever: {e}")
            raise ValueError(f"Semantic retriever initialization failed: {e}")

        # Initialize Gemini client with error handling
        try:
            logger.info("Initializing Gemini client...")
            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            gemini_client = GeminiClient(api_key=Config.GOOGLE_API_KEY, model_name=Config.GEMINI_MODEL)

            # Validate Gemini connection
            if not gemini_client.validate_api_key():
                raise ValueError("Failed to validate Google API key")
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            raise ValueError(f"Gemini client initialization failed: {e}")
        
        system_initialized = True
        logger.info("System initialization completed successfully")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        system_initialized = False
        raise


def status_callback(message: str, level: str = "info"):
    """Status callback function for processing updates."""
    if level == "error":
        logger.error(message)
    elif level == "warning":
        logger.warning(message)
    elif level == "success":
        logger.info(f"SUCCESS: {message}")
    else:
        logger.info(message)


def detect_missing_information_response(response_text: str) -> bool:
    """
    Detect if the LLM response indicates missing information.

    Args:
        response_text (str): The response text from the LLM

    Returns:
        bool: True if the response indicates missing information
    """
    # Common phrases that indicate missing information
    missing_info_indicators = [
        "the document does not contain",
        "not mentioned in the document",
        "no information about",
        "not found in the document",
        "document doesn't mention",
        "not available in the document",
        "no details about",
        "not specified in the document",
        "document does not provide",
        "not included in the document",
        "no mention of",
        "document lacks information",
        "not covered in the document",
        "document doesn't include",
        "information is not available",
        "not discussed in the document",
        "document doesn't contain",
        "no data about",
        "not referenced in the document",
        "document doesn't specify"
    ]

    response_lower = response_text.lower()
    return any(indicator in response_lower for indicator in missing_info_indicators)


def get_friendly_fallback_response() -> str:
    """
    Get a user-friendly fallback response for missing information.

    Returns:
        str: A friendly fallback message
    """
    import random

    fallback_responses = [
        "Sorry, I don't have information on this topic.",
        "I couldn't find relevant information to answer your question.",
        "I don't have details about that in my knowledge base.",
        "Sorry, I don't have information about that topic.",
        "I couldn't locate information on this subject.",
        "That information isn't available in my current knowledge base."
    ]

    return random.choice(fallback_responses)


async def process_pdf_if_needed():
    """Process PDF and build index if not already done with comprehensive error handling."""
    global vector_db

    try:
        # Check if processing is needed
        if not vector_db:
            logger.error("Vector database not initialized")
            return

        if vector_db.index is not None:
            logger.info("Index already exists, skipping PDF processing")
            return  # Index already exists

        # Check if PDF file exists
        pdf_path = "skuast.pdf"
        if not Path(pdf_path).exists():
            error_msg = f"PDF file not found: {pdf_path}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        status_callback("Processing PDF and building vector index...", "info")

        # Process PDF with error handling
        try:
            pdf_processor = PDFProcessor()
            markdown_content = pdf_processor.process_pdf(pdf_path)
        except Exception as e:
            error_msg = f"Error processing PDF file: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate PDF content
        if not markdown_content or len(markdown_content.strip()) < 100:
            status_callback("PDF processing resulted in insufficient content", "warning")
            return

        # Chunk text with error handling
        try:
            chunking_config = Config.get_chunking_config()
            chunker = TextChunker(
                chunk_size=chunking_config['chunk_size'],
                overlap_size=chunking_config['overlap_size']
            )
            chunks = chunker.split_text_into_chunks(markdown_content)
        except Exception as e:
            error_msg = f"Error chunking text: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate chunks
        if not chunks:
            status_callback("Text chunking resulted in no chunks", "error")
            return

        # Build vector index with error handling
        try:
            build_success = vector_db.build_index(chunks, status_callback=status_callback)
        except Exception as e:
            error_msg = f"Error building vector index: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Save index with error handling
        if build_success:
            try:
                vector_db.save_index("skuast_index")
                status_callback(f"Successfully processed PDF and created index with {len(chunks)} chunks", "success")
            except Exception as e:
                error_msg = f"Error saving index: {e}"
                logger.error(error_msg)
                status_callback(error_msg, "error")
        else:
            status_callback("Failed to build vector index", "error")

    except Exception as e:
        error_msg = f"Unexpected error processing PDF: {e}"
        logger.error(error_msg)
        status_callback(error_msg, "error")


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    await initialize_system()
    await process_pdf_if_needed()
    yield
    # Shutdown
    logger.info("Shutting down SKUAST RAG system")


# Create FastAPI app
app = FastAPI(
    title="SKUAST RAG Chatbot",
    description="A Retrieval-Augmented Generation chatbot for SKUAST (Sher-e-Kashmir University of Agricultural Sciences and Technology)",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# @app.get("/", response_model=Dict[str, str])
# async def root():
#     """Root endpoint with basic information."""
#     return {
#         "message": "SKUAST RAG Chatbot API",
#         "description": "Ask questions about Sher-e-Kashmir University of Agricultural Sciences and Technology",
#         "endpoints": {
#             "chat": "POST /chat - Ask questions about SKUAST",
#             "status": "GET /status - Check system status",
#             "health": "GET /health - Health check"
#         }
#     }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat endpoint for asking questions about SKUAST.
    """
    try:
        # System initialization check
        if not system_initialized:
            logger.error("Chat request received but system not initialized")
            raise HTTPException(
                status_code=503,
                detail="System is not ready. Please try again in a moment."
            )

        # Component availability check
        if not all([vector_db, retriever, gemini_client]):
            logger.error("Chat request received but system components not available")
            raise HTTPException(
                status_code=503,
                detail="System components are not available. Please try again later."
            )

        # Input validation
        if not request.query or not request.query.strip():
            logger.warning("Empty query received")
            raise HTTPException(
                status_code=400,
                detail="Query cannot be empty"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in initial chat validation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during request validation"
        )
    
    # Main processing with comprehensive error handling
    try:
        logger.info(f"Processing chat request: '{request.query[:50]}...'")

        # Use hardcoded values for top_k and temperature
        top_k = 3  # Hardcoded default value
        temperature = 0.3  # Hardcoded default value

        # Create context using retriever with error handling
        try:
            context_data = retriever.create_context_for_llm(
                query=request.query,
                top_k=top_k
            )
        except Exception as e:
            logger.error(f"Error during context retrieval: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving relevant information. Please try again."
            )

        # Check if context was found
        if not context_data or context_data.get('num_sources', 0) == 0:
            logger.info("No relevant context found for query")
            return ChatResponse(
                response="Sorry, I don't have information on this topic."
            )
        
        # Generate response using Gemini with comprehensive error handling
        try:
            logger.info("Generating response using Gemini")
            gemini_response = gemini_client.generate_response(
                query=request.query,
                context=context_data['context'],
                temperature=temperature
            )

            # Check if response generation was successful
            if not gemini_response or not gemini_response.get('success', False):
                error_msg = gemini_response.get('error', 'Unknown error') if gemini_response else 'No response received'
                logger.warning(f"Gemini generation failed: {error_msg}")
                return ChatResponse(
                    response="Sorry, I couldn't generate a response at the moment. Please try again."
                )

            # Validate response content
            if not gemini_response.get('response') or not gemini_response['response'].strip():
                logger.warning("Gemini returned empty response")
                return ChatResponse(
                    response="Sorry, I couldn't generate a meaningful response. Please try rephrasing your question."
                )

        except ConnectionError as e:
            logger.error(f"Network connection error during Gemini API call: {e}")
            raise HTTPException(
                status_code=503,
                detail="Service temporarily unavailable. Please try again later."
            )
        except TimeoutError as e:
            logger.error(f"Timeout error during Gemini API call: {e}")
            raise HTTPException(
                status_code=504,
                detail="Request timeout. Please try again."
            )
        except Exception as e:
            logger.error(f"Unexpected error during response generation: {e}")
            return ChatResponse(
                response="Sorry, I encountered an error while processing your question. Please try again."
            )
        
        # Check if the response indicates missing information and replace with friendly message
        try:
            response_text = gemini_response['response']
            if detect_missing_information_response(response_text):
                response_text = get_friendly_fallback_response()
                logger.info("Replaced missing information response with friendly fallback")
        except Exception as e:
            logger.error(f"Error processing response text: {e}")
            response_text = "Sorry, I encountered an error while processing the response."

        # Successful response
        return ChatResponse(
            response=response_text
        )

    except HTTPException:
        # Re-raise HTTP exceptions (they're already handled)
        raise
    except ValueError as e:
        logger.error(f"Validation error in chat processing: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid request data"
        )
    except ConnectionError as e:
        logger.error(f"Connection error in chat processing: {e}")
        raise HTTPException(
            status_code=503,
            detail="Service temporarily unavailable"
        )
    except TimeoutError as e:
        logger.error(f"Timeout error in chat processing: {e}")
        raise HTTPException(
            status_code=504,
            detail="Request timeout"
        )
    except Exception as e:
        logger.error(f"Unexpected error processing chat request: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred. Please try again."
        )


# @app.get("/status", response_model=SystemStatus)
# async def get_status():
#     """Get system status and component information."""
#     try:
#         components = {}
        
#         # Vector database status
#         if vector_db:
#             components["vector_database"] = vector_db.get_index_stats()
#         else:
#             components["vector_database"] = {"status": "Not initialized"}
        
#         # Retriever status
#         if retriever:
#             components["retriever"] = retriever.get_retrieval_stats()
#         else:
#             components["retriever"] = {"status": "Not initialized"}
        
#         # Gemini client status
#         if gemini_client:
#             components["gemini_client"] = gemini_client.get_model_info()
#         else:
#             components["gemini_client"] = {"status": "Not initialized"}
        
#         # Overall status
#         overall_status = "Ready" if system_initialized else "Not Ready"
        
#         # Statistics
#         statistics = {
#             "system_initialized": system_initialized,
#             "pdf_processed": vector_db.index is not None if vector_db else False,
#             "api_key_configured": os.getenv('GOOGLE_API_KEY') is not None
#         }
        
#         return SystemStatus(
#             status=overall_status,
#             components=components,
#             statistics=statistics
#         )
        
#     except Exception as e:
#         logger.error(f"Error getting system status: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Error retrieving system status: {str(e)}"
#         )


# @app.get("/health")
# async def health_check():
#     """Simple health check endpoint."""
#     return {
#         "status": "healthy" if system_initialized else "unhealthy",
#         "timestamp": "2024-01-01T00:00:00Z"  # You might want to use actual timestamp
#     }


# @app.post("/rebuild-index")
# async def rebuild_index(background_tasks: BackgroundTasks):
    """Rebuild the vector index (admin endpoint)."""
    if not system_initialized:
        raise HTTPException(
            status_code=503,
            detail="System not initialized"
        )
    
    background_tasks.add_task(process_pdf_if_needed)
    
    return {
        "message": "Index rebuild started in background",
        "status": "processing"
    }


if __name__ == "__main__":
    import uvicorn

    # Get API configuration
    api_config = Config.get_api_config()

    # Run the application
    uvicorn.run(
        "main:app",
        host=api_config['host'],
        port=api_config['port'],
        reload=api_config['reload'],
        log_level="info"
    )
