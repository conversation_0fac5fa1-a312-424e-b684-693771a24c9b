{"pdf_processor": {"status": "PASSED", "extraction_time": "1.31s", "raw_text_length": 89389, "cleaned_text_length": 71449, "markdown_length": 71449, "has_content": true}, "text_chunker": {"status": "PASSED", "chunking_time": "0.00s", "num_chunks": 5, "avg_chunk_length": 804.4, "total_characters": 4022, "overlap_preserved": true}, "vector_database": {"status": "PASSED", "build_time": "0.74s", "search_time": "0.114s", "index_stats": {"status": "Index loaded", "model_name": "all-MiniLM-L6-v2", "embedding_dimension": 384, "total_vectors": 2, "total_chunks": 2, "index_type": "IndexFlatIP", "total_characters": 125, "total_tokens": 21, "avg_chunk_length": 62.5, "avg_tokens_per_chunk": 10.5}, "search_results": 2, "embedding_dimension": 384}, "semantic_retriever": {"status": "PASSED", "retrieval_time": "0.050s", "chunks_retrieved": 2, "context_created": true, "avg_relevance_score": 0.5012276023626328}, "gemini_client": {"status": "SKIPPED", "reason": "GOOGLE_API_KEY not set", "recommendation": "Set GOOGLE_API_KEY environment variable"}, "api_endpoints": {"status": "SKIPPED", "reason": "Server not running", "recommendation": "Start the server with: python main.py"}}