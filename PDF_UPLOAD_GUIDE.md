# PDF Upload and Temporary Database System

## Overview

The SKUAST RAG system now supports uploading multiple PDF files and creating temporary vector databases for querying. This allows users to upload their own documents and get AI-powered responses based on the uploaded content, separate from the main SKUAST knowledge base.

## Key Features

### 🔄 **Multi-File PDF Processing**
- Upload up to 5 PDF files simultaneously
- Automatic text extraction and markdown conversion
- Intelligent text chunking for optimal retrieval
- File validation (PDF format, size limits)

### 🗄️ **Temporary Vector Databases**
- Each upload session creates an isolated vector database
- Google embeddings for semantic search
- Session-based access control
- Automatic cleanup after 1 hour

### 🔍 **Natural Language Querying**
- Query uploaded documents using natural language
- Same simplified API as main chat endpoint
- Context-aware responses based on uploaded content
- No technical document references in responses

### 🧹 **Session Management**
- Unique session IDs for each upload
- Manual and automatic cleanup options
- Background cleanup every 30 minutes
- Complete cleanup on server shutdown

## API Endpoints

### 1. Upload PDFs

**Endpoint:** `POST /upload-pdfs`

**Description:** Upload multiple PDF files and create a temporary vector database.

**Request:**
- Content-Type: `multipart/form-data`
- Files: Up to 5 PDF files (max 10MB each)

**Example:**
```bash
curl -X POST \
  -F "files=@document1.pdf" \
  -F "files=@document2.pdf" \
  http://localhost:8000/upload-pdfs
```

**Response:**
```json
{
  "session_id": "session_abc123_1234567890",
  "files_processed": 2,
  "total_chunks": 45,
  "processing_errors": [],
  "database_path": "data/temp_uploads/session_abc123_1234567890",
  "index_stats": {
    "total_vectors": 45,
    "embedding_dimension": 768,
    "index_type": "IndexFlatIP"
  }
}
```

### 2. Query Temporary Database

**Endpoint:** `POST /query-temp-db/{session_id}`

**Description:** Query a temporary database created from uploaded PDFs.

**Request:**
```json
{
  "query": "What is the main topic of the uploaded documents?"
}
```

**Example:**
```bash
curl -X POST \
  -H "Content-Type: application/json" \
  -d '{"query": "What farming techniques are mentioned?"}' \
  http://localhost:8000/query-temp-db/session_abc123_1234567890
```

**Response:**
```json
{
  "response": "The documents discuss several farming techniques including crop rotation, organic fertilization, and sustainable irrigation methods."
}
```

### 3. Delete Temporary Database

**Endpoint:** `DELETE /clear-temp-db/{session_id}`

**Description:** Manually delete a specific temporary database.

**Example:**
```bash
curl -X DELETE http://localhost:8000/clear-temp-db/session_abc123_1234567890
```

**Response:**
```json
{
  "message": "Successfully deleted temporary database 'session_abc123_1234567890'",
  "deleted_sessions": ["session_abc123_1234567890"]
}
```

### 4. Cleanup Old Databases

**Endpoint:** `POST /cleanup-temp-dbs?max_age_hours=2`

**Description:** Manually trigger cleanup of old temporary databases.

**Example:**
```bash
curl -X POST http://localhost:8000/cleanup-temp-dbs?max_age_hours=2
```

**Response:**
```json
{
  "message": "Cleaned up 3 temporary databases older than 2 hours",
  "deleted_sessions": ["session_old1_123", "session_old2_456", "session_old3_789"]
}
```

## File Validation Rules

### **Supported Formats**
- PDF files only (`.pdf` extension)
- MIME type: `application/pdf`

### **Size Limits**
- Maximum file size: 10MB per file
- Maximum files per upload: 5 files
- Total upload size: 50MB maximum

### **Content Requirements**
- Files must contain extractable text
- Minimum content length: 100 characters after extraction
- Password-protected PDFs are not supported

## Error Handling

### **Common Error Scenarios**

1. **File Validation Errors (400)**
   ```json
   {
     "detail": "File validation failed: File 'document.txt' is not a PDF file"
   }
   ```

2. **Too Many Files (400)**
   ```json
   {
     "detail": "Maximum 5 files allowed per upload"
   }
   ```

3. **Processing Errors (400)**
   ```json
   {
     "detail": "No files could be processed. Errors: File 'corrupted.pdf': Failed to extract text"
   }
   ```

4. **Session Not Found (404)**
   ```json
   {
     "detail": "Session 'session_invalid_123' not found or has expired"
   }
   ```

5. **Server Errors (500)**
   ```json
   {
     "detail": "Failed to build vector index from uploaded files"
   }
   ```

## Usage Examples

### **Python Example**
```python
import requests

# Upload files
files = [
    ('files', ('doc1.pdf', open('doc1.pdf', 'rb'), 'application/pdf')),
    ('files', ('doc2.pdf', open('doc2.pdf', 'rb'), 'application/pdf'))
]

response = requests.post('http://localhost:8000/upload-pdfs', files=files)
result = response.json()
session_id = result['session_id']

# Query the database
query_response = requests.post(
    f'http://localhost:8000/query-temp-db/{session_id}',
    json={'query': 'What is this document about?'}
)
answer = query_response.json()['response']

# Cleanup
requests.delete(f'http://localhost:8000/clear-temp-db/{session_id}')
```

### **JavaScript Example**
```javascript
// Upload files
const formData = new FormData();
formData.append('files', file1);
formData.append('files', file2);

const uploadResponse = await fetch('/upload-pdfs', {
    method: 'POST',
    body: formData
});
const uploadResult = await uploadResponse.json();
const sessionId = uploadResult.session_id;

// Query the database
const queryResponse = await fetch(`/query-temp-db/${sessionId}`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ query: 'What is this about?' })
});
const queryResult = await queryResponse.json();
console.log(queryResult.response);

// Cleanup
await fetch(`/clear-temp-db/${sessionId}`, { method: 'DELETE' });
```

## Session Management

### **Session ID Format**
- Format: `session_{random_12_chars}_{timestamp}`
- Example: `session_a1b2c3d4e5f6_1703123456`

### **Session Lifecycle**
1. **Creation:** When PDFs are uploaded successfully
2. **Active:** Available for querying until cleanup
3. **Cleanup:** Automatic after 1 hour or manual deletion
4. **Expiry:** Session becomes invalid after cleanup

### **Automatic Cleanup**
- **Frequency:** Every 30 minutes
- **Age Threshold:** 1 hour by default
- **Scope:** All expired sessions
- **Actions:** Delete from memory and remove files

## Performance Considerations

### **Upload Performance**
- Large files take longer to process
- Multiple files are processed sequentially
- Embedding generation may take time for large documents

### **Query Performance**
- First query may be slower (index loading)
- Subsequent queries are faster (cached in memory)
- Response time depends on document size and complexity

### **Memory Usage**
- Each session maintains vector database in memory
- Automatic cleanup prevents memory leaks
- Monitor server resources with many concurrent sessions

## Security Considerations

### **File Security**
- Uploaded files are stored temporarily
- Files are deleted after session cleanup
- No persistent storage of user documents

### **Session Security**
- Session IDs are randomly generated
- No authentication required (consider adding for production)
- Sessions are isolated from each other

### **Content Privacy**
- Documents are processed locally
- No data sent to external services except Google embeddings API
- Temporary storage only

## Testing

### **Run Tests**
```bash
# Basic functionality test
python test_pdf_upload_system.py

# Include error scenario tests
python test_pdf_upload_system.py --test-errors

# Interactive demo
python demo_pdf_upload.py

# Show API endpoints
python demo_pdf_upload.py --show-endpoints
```

### **Manual Testing**
1. Upload PDF files using the `/upload-pdfs` endpoint
2. Query the temporary database using the session ID
3. Test error scenarios (invalid files, large files, etc.)
4. Verify cleanup functionality

## Troubleshooting

### **Common Issues**

1. **"No files could be processed"**
   - Check if PDFs contain extractable text
   - Verify files are not corrupted or password-protected
   - Ensure files meet size requirements

2. **"Session not found"**
   - Session may have expired (>1 hour old)
   - Check session ID spelling
   - Session may have been manually deleted

3. **"Failed to build vector index"**
   - Google API quota may be exceeded
   - Check Google API key configuration
   - Verify network connectivity

4. **Slow processing**
   - Large files take more time
   - Google API rate limiting may apply
   - Server resources may be limited

### **Debugging**
- Check server logs for detailed error messages
- Monitor API quota usage in Google Cloud Console
- Verify file permissions for temporary directories
- Test with smaller files first

## Integration with Main System

### **Isolation**
- Temporary databases are completely separate from main SKUAST database
- No interference with main chat functionality
- Independent session management

### **Shared Components**
- Uses same PDF processing pipeline
- Same text chunking configuration
- Same Google embeddings API
- Same Gemini LLM for responses

### **Configuration**
- Inherits settings from main configuration
- Uses same API keys and credentials
- Follows same error handling patterns
