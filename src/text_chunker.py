"""
Text chunking module for splitting documents into overlapping chunks.
"""

import re
import logging
from typing import List, Dict, Any
import tiktoken

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TextChunker:
    """
    A class to handle text chunking with configurable chunk size and overlap.
    """
    
    def __init__(self, chunk_size: int = 5000, overlap_size: int = 1000):
        """
        Initialize the text chunker.
        
        Args:
            chunk_size (int): Maximum size of each chunk in characters
            overlap_size (int): Number of characters to overlap between chunks
        """
        self.chunk_size = chunk_size
        self.overlap_size = overlap_size
        
        # Initialize tokenizer for more accurate token counting
        try:
            self.tokenizer = tiktoken.get_encoding("cl100k_base")
        except Exception as e:
            logger.warning(f"Could not initialize tokenizer: {e}")
            self.tokenizer = None
        
        logger.info(f"TextChunker initialized with chunk_size={chunk_size}, overlap_size={overlap_size}")
    
    def split_text_into_chunks(self, text: str, preserve_structure: bool = True) -> List[Dict[str, Any]]:
        """
        Split text into overlapping chunks.
        
        Args:
            text (str): Input text to be chunked
            preserve_structure (bool): Whether to try to preserve markdown structure
            
        Returns:
            List[Dict[str, Any]]: List of chunk dictionaries with metadata
        """
        if not text or not text.strip():
            logger.warning("Empty text provided for chunking")
            return []
        
        logger.info(f"Starting text chunking. Input length: {len(text)} characters")
        
        if preserve_structure:
            chunks = self._chunk_with_structure_preservation(text)
        else:
            chunks = self._chunk_simple(text)
        
        # Add metadata to chunks
        processed_chunks = []
        for i, chunk_text in enumerate(chunks):
            chunk_data = {
                'id': f"chunk_{i+1}",
                'text': chunk_text,
                'length': len(chunk_text),
                'token_count': self._count_tokens(chunk_text),
                'chunk_index': i,
                'total_chunks': len(chunks)
            }
            processed_chunks.append(chunk_data)
        
        logger.info(f"Created {len(processed_chunks)} chunks")
        return processed_chunks
    
    def _chunk_with_structure_preservation(self, text: str) -> List[str]:
        """
        Chunk text while trying to preserve markdown structure.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of text chunks
        """
        # Split text into sections based on headers
        sections = self._split_by_headers(text)
        
        chunks = []
        current_chunk = ""
        
        for section in sections:
            # If adding this section would exceed chunk size
            if len(current_chunk) + len(section) > self.chunk_size:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                    
                    # Start new chunk with overlap from previous chunk
                    if self.overlap_size > 0:
                        overlap_text = self._get_overlap_text(current_chunk, self.overlap_size)
                        current_chunk = overlap_text
                    else:
                        current_chunk = ""
                
                # If section itself is larger than chunk size, split it
                if len(section) > self.chunk_size:
                    section_chunks = self._split_large_section(section)
                    for i, section_chunk in enumerate(section_chunks):
                        if i == 0 and current_chunk:
                            chunks.append((current_chunk + "\n\n" + section_chunk).strip())
                            current_chunk = ""
                        else:
                            chunks.append(section_chunk.strip())
                else:
                    current_chunk += "\n\n" + section if current_chunk else section
            else:
                current_chunk += "\n\n" + section if current_chunk else section
        
        # Add remaining chunk
        if current_chunk.strip():
            chunks.append(current_chunk.strip())
        
        return chunks
    
    def _chunk_simple(self, text: str) -> List[str]:
        """
        Simple chunking without structure preservation.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of text chunks
        """
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + self.chunk_size
            
            # If this is not the last chunk, try to find a good break point
            if end < len(text):
                # Look for sentence endings near the chunk boundary
                break_point = self._find_break_point(text, start, end)
                if break_point > start:
                    end = break_point
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            # Move start position with overlap
            start = max(start + 1, end - self.overlap_size)
        
        return chunks
    
    def _split_by_headers(self, text: str) -> List[str]:
        """
        Split text into sections based on markdown headers.
        
        Args:
            text (str): Input text
            
        Returns:
            List[str]: List of text sections
        """
        # Pattern to match markdown headers
        header_pattern = r'^(#{1,6}\s+.+)$'
        
        lines = text.split('\n')
        sections = []
        current_section = []
        
        for line in lines:
            if re.match(header_pattern, line.strip()):
                # Save previous section
                if current_section:
                    sections.append('\n'.join(current_section).strip())
                    current_section = []
                
                # Start new section with header
                current_section.append(line)
            else:
                current_section.append(line)
        
        # Add last section
        if current_section:
            sections.append('\n'.join(current_section).strip())
        
        return [section for section in sections if section.strip()]
    
    def _split_large_section(self, section: str) -> List[str]:
        """
        Split a large section that exceeds chunk size.
        
        Args:
            section (str): Large section text
            
        Returns:
            List[str]: List of smaller chunks
        """
        chunks = []
        start = 0
        
        while start < len(section):
            end = start + self.chunk_size
            
            if end < len(section):
                # Find a good break point
                break_point = self._find_break_point(section, start, end)
                if break_point > start:
                    end = break_point
            
            chunk = section[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = max(start + 1, end - self.overlap_size)
        
        return chunks
    
    def _find_break_point(self, text: str, start: int, end: int) -> int:
        """
        Find a good break point near the chunk boundary.
        
        Args:
            text (str): Full text
            start (int): Start position
            end (int): Desired end position
            
        Returns:
            int: Actual break point position
        """
        # Look for break points in order of preference
        search_start = max(start, end - 200)  # Look within last 200 chars
        search_text = text[search_start:end]
        
        # 1. Look for paragraph breaks (double newlines)
        paragraph_breaks = [m.end() for m in re.finditer(r'\n\s*\n', search_text)]
        if paragraph_breaks:
            return search_start + paragraph_breaks[-1]
        
        # 2. Look for sentence endings
        sentence_endings = [m.end() for m in re.finditer(r'[.!?]\s+', search_text)]
        if sentence_endings:
            return search_start + sentence_endings[-1]
        
        # 3. Look for line breaks
        line_breaks = [m.end() for m in re.finditer(r'\n', search_text)]
        if line_breaks:
            return search_start + line_breaks[-1]
        
        # 4. Look for word boundaries
        word_boundaries = [m.start() for m in re.finditer(r'\s+', search_text)]
        if word_boundaries:
            return search_start + word_boundaries[-1]
        
        # If no good break point found, use the original end
        return end
    
    def _get_overlap_text(self, text: str, overlap_size: int) -> str:
        """
        Get overlap text from the end of a chunk.
        
        Args:
            text (str): Source text
            overlap_size (int): Size of overlap
            
        Returns:
            str: Overlap text
        """
        if len(text) <= overlap_size:
            return text
        
        # Try to find a good starting point for overlap
        start_pos = len(text) - overlap_size
        
        # Look for sentence or paragraph boundaries
        search_text = text[start_pos:]
        
        # Look for sentence starts
        sentence_starts = [m.start() for m in re.finditer(r'[.!?]\s+[A-Z]', search_text)]
        if sentence_starts:
            return text[start_pos + sentence_starts[0]:].strip()
        
        # Look for line breaks
        line_breaks = [m.start() for m in re.finditer(r'\n', search_text)]
        if line_breaks:
            return text[start_pos + line_breaks[0]:].strip()
        
        # Use the full overlap
        return text[-overlap_size:].strip()
    
    def _count_tokens(self, text: str) -> int:
        """
        Count tokens in text using tiktoken if available.
        
        Args:
            text (str): Input text
            
        Returns:
            int: Token count
        """
        if self.tokenizer:
            try:
                return len(self.tokenizer.encode(text))
            except Exception as e:
                logger.warning(f"Error counting tokens: {e}")
        
        # Fallback: rough estimation (1 token ≈ 4 characters)
        return len(text) // 4
    
    def get_chunk_statistics(self, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Get statistics about the chunks.
        
        Args:
            chunks (List[Dict[str, Any]]): List of chunk dictionaries
            
        Returns:
            Dict[str, Any]: Statistics dictionary
        """
        if not chunks:
            return {}
        
        lengths = [chunk['length'] for chunk in chunks]
        token_counts = [chunk['token_count'] for chunk in chunks]
        
        stats = {
            'total_chunks': len(chunks),
            'total_characters': sum(lengths),
            'total_tokens': sum(token_counts),
            'avg_chunk_length': sum(lengths) / len(lengths),
            'avg_token_count': sum(token_counts) / len(token_counts),
            'min_chunk_length': min(lengths),
            'max_chunk_length': max(lengths),
            'chunk_size_setting': self.chunk_size,
            'overlap_size_setting': self.overlap_size
        }
        
        return stats


def main():
    """
    Main function for testing the text chunker.
    """
    # Test with sample text
    sample_text = """
    # Introduction
    This is a sample document for testing the text chunker.
    
    ## Section 1
    This section contains some content that we want to split into chunks.
    The chunker should preserve the structure while creating overlapping segments.
    
    ## Section 2
    This is another section with more content.
    We want to ensure that the chunking works properly with different types of content.
    
    ### Subsection 2.1
    This is a subsection with additional details.
    """ * 10  # Repeat to make it longer
    
    chunker = TextChunker(chunk_size=500, overlap_size=100)
    chunks = chunker.split_text_into_chunks(sample_text)
    
    print(f"Created {len(chunks)} chunks")
    
    for i, chunk in enumerate(chunks[:3]):  # Show first 3 chunks
        print(f"\n--- Chunk {i+1} ---")
        print(f"Length: {chunk['length']} characters")
        print(f"Tokens: {chunk['token_count']}")
        print(f"Text preview: {chunk['text'][:200]}...")
    
    # Show statistics
    stats = chunker.get_chunk_statistics(chunks)
    print(f"\n--- Statistics ---")
    for key, value in stats.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    main()
