#!/usr/bin/env python3
"""
Test script to verify the simplified approach works correctly.
Tests that the system prompt prevents document references at the source.
"""

import requests
import json
import time


def test_simplified_system(base_url="http://localhost:8000"):
    """Test the simplified system with enhanced system prompt."""
    
    print("🎯 Testing Simplified Document Reference Prevention")
    print("=" * 70)
    print("This test verifies that the enhanced system prompt prevents")
    print("document references at the source (LLM generation level)")
    print("=" * 70)
    
    # Test cases that previously would generate document references
    test_cases = [
        {
            "query": "Who is the current director of SKUAST?",
            "description": "Leadership question (previously: 'the document states')"
        },
        {
            "query": "What programs does SKUAST offer?",
            "description": "Program information (previously: 'the provided text')"
        },
        {
            "query": "When was SKUAST established?",
            "description": "Historical information (previously: 'according to the document')"
        },
        {
            "query": "What are the admission requirements?",
            "description": "Requirements (previously: 'the text mentions')"
        },
        {
            "query": "What are the tuition fees for international students?",
            "description": "Missing information (should be friendly, no document refs)"
        },
        {
            "query": "What is the campus WiFi password?",
            "description": "Clearly missing info (should be natural response)"
        }
    ]
    
    print(f"Testing API at: {base_url}")
    print(f"Number of test queries: {len(test_cases)}")
    print()
    
    results = []
    
    for i, test_case in enumerate(test_cases, 1):
        query = test_case["query"]
        description = test_case["description"]
        
        print(f"🔍 Test {i}: {query}")
        print(f"📝 Purpose: {description}")
        print("-" * 50)
        
        try:
            # Make API request
            response = requests.post(
                f"{base_url}/chat",
                json={"query": query},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                response_text = data.get('response', '')
                
                print(f"✅ Response: {response_text}")
                
                # Check for document references
                doc_ref_phrases = [
                    "the document", "the provided text", "the text states",
                    "according to the document", "based on the text",
                    "the document states", "the text mentions",
                    "the provided text states", "document mentions",
                    "text indicates", "document shows", "text shows"
                ]
                
                has_doc_refs = any(phrase in response_text.lower() for phrase in doc_ref_phrases)
                
                if has_doc_refs:
                    print("❌ FAIL: Response contains document references!")
                    # Find which phrases were detected
                    found_phrases = [phrase for phrase in doc_ref_phrases if phrase in response_text.lower()]
                    print(f"   Found: {', '.join(found_phrases)}")
                    results.append(False)
                else:
                    print("✅ PASS: Response is clean (no document references)")
                    results.append(True)
                    
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"Error: {response.text}")
                results.append(False)
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection error: {e}")
            results.append(False)
        
        print()
        time.sleep(1)  # Small delay between requests
    
    # Summary
    passed = sum(results)
    total = len(results)
    
    print("=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    print(f"Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 SUCCESS: All responses are clean of document references!")
        print("✅ The simplified approach with enhanced system prompt is working correctly.")
    else:
        print("⚠️  Some responses still contain document references.")
        print("   The system prompt may need further refinement.")
    
    return passed == total


def check_server_status(base_url="http://localhost:8000"):
    """Check if the server is running."""
    try:
        response = requests.get(f"{base_url}/docs", timeout=5)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False


def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the simplified document reference prevention")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    
    args = parser.parse_args()
    
    # Check if server is running
    if not check_server_status(args.url):
        print(f"❌ Server not accessible at {args.url}")
        print("Make sure the server is running: python main.py")
        return 1
    
    # Run the test
    success = test_simplified_system(args.url)
    
    print("\n" + "=" * 70)
    if success:
        print("🎉 All tests passed! The simplified approach is working perfectly.")
        print("✅ System prompt successfully prevents document references at the source.")
    else:
        print("⚠️  Some tests failed. The system prompt may need adjustment.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
