#!/usr/bin/env python3
"""
Test the fixed vector database with Google embeddings.
"""

import os
import sys
import tempfile
import shutil

# Add src to path
sys.path.append('src')

from vector_db import VectorDatabase


def test_vector_db():
    """Test the vector database with Google embeddings."""
    
    # Check API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY not found in environment variables")
        return False
    
    print(f"✅ Using API key: {api_key[:10]}...")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Initialize vector database
        print("\n🔍 Initializing VectorDatabase...")
        vector_db = VectorDatabase(
            model_name="models/text-embedding-004",
            db_path=temp_dir
        )
        print("✅ VectorDatabase initialized")
        
        # Test embedding creation
        print("\n🔍 Testing embedding creation...")
        test_texts = [
            "SKUAST is a university focused on agricultural sciences.",
            "The university offers various programs in agriculture.",
            "Research is conducted in multiple agricultural fields."
        ]
        
        embeddings = vector_db.create_embeddings(test_texts)
        print(f"✅ Created embeddings with shape: {embeddings.shape}")
        
        # Test index building
        print("\n🔍 Testing index building...")
        chunks = []
        for i, text in enumerate(test_texts):
            chunks.append({
                'id': f'chunk_{i}',
                'text': text,
                'length': len(text),
                'chunk_index': i,
                'token_count': len(text.split())
            })
        
        success = vector_db.build_index(chunks)
        if success:
            print("✅ Index built successfully")
        else:
            print("❌ Index building failed")
            return False
        
        # Test search
        print("\n🔍 Testing search...")
        results = vector_db.search("agricultural research", top_k=2)
        
        if results:
            print(f"✅ Search successful: found {len(results)} results")
            for result in results:
                print(f"  - Score: {result['score']:.4f}, Text: {result['text'][:50]}...")
        else:
            print("❌ Search returned no results")
            return False
        
        print("\n✅ All tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False
        
    finally:
        # Clean up
        if os.path.exists(temp_dir):
            shutil.rmtree(temp_dir)


if __name__ == "__main__":
    print("🚀 Vector Database Fix Test")
    print("=" * 50)
    
    success = test_vector_db()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 Vector database is working correctly!")
        print("You can now restart the main application.")
    else:
        print("❌ Vector database test failed.")
        print("Please check the error messages above.")
