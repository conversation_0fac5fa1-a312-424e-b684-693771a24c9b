#!/usr/bin/env python3
"""
Debug script to test Google embeddings API and understand the response structure.
"""

import os
import google.generativeai as genai


def test_google_embeddings():
    """Test Google embeddings API to understand response structure."""
    
    # Check API key
    api_key = os.getenv('GOOGLE_API_KEY')
    if not api_key:
        print("❌ GOOGLE_API_KEY not found in environment variables")
        return
    
    print(f"✅ Using API key: {api_key[:10]}...")
    
    # Configure API
    genai.configure(api_key=api_key)
    
    # Test single text embedding
    print("\n🔍 Testing single text embedding...")
    try:
        result = genai.embed_content(
            model="models/text-embedding-004",
            content="This is a test sentence.",
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        print(f"Result type: {type(result)}")
        print(f"Result attributes: {dir(result)}")
        
        if hasattr(result, 'embedding'):
            print(f"✅ Has 'embedding' attribute")
            print(f"Embedding type: {type(result.embedding)}")
            print(f"Embedding length: {len(result.embedding) if result.embedding else 'None'}")
            if result.embedding:
                print(f"First 5 values: {result.embedding[:5]}")
        else:
            print("❌ No 'embedding' attribute")
        
        if isinstance(result, dict):
            print(f"Result as dict keys: {result.keys()}")
        
        # Try to access as different structures
        try:
            embedding = result.embedding
            print(f"✅ Successfully accessed result.embedding: {len(embedding)} dimensions")
        except:
            print("❌ Failed to access result.embedding")
        
        try:
            if isinstance(result, dict):
                embedding = result['embedding']
                print(f"✅ Successfully accessed result['embedding']: {len(embedding)} dimensions")
        except:
            print("❌ Failed to access result['embedding']")
            
    except Exception as e:
        print(f"❌ Single text embedding failed: {e}")
    
    # Test query embedding
    print("\n🔍 Testing query embedding...")
    try:
        result = genai.embed_content(
            model="models/text-embedding-004",
            content="What is agriculture?",
            task_type="RETRIEVAL_QUERY"
        )
        
        print(f"Query result type: {type(result)}")
        
        if hasattr(result, 'embedding') and result.embedding:
            print(f"✅ Query embedding successful: {len(result.embedding)} dimensions")
        else:
            print("❌ Query embedding failed")
            
    except Exception as e:
        print(f"❌ Query embedding failed: {e}")
    
    # Test multiple texts (batch)
    print("\n🔍 Testing batch embedding...")
    try:
        texts = ["First test sentence.", "Second test sentence."]
        
        # Try batch approach
        result = genai.embed_content(
            model="models/text-embedding-004",
            content=texts,
            task_type="RETRIEVAL_DOCUMENT"
        )
        
        print(f"Batch result type: {type(result)}")
        print(f"Batch result attributes: {dir(result)}")
        
        if hasattr(result, 'embeddings'):
            print(f"✅ Has 'embeddings' attribute: {len(result.embeddings)} embeddings")
        elif hasattr(result, 'embedding'):
            print(f"✅ Has 'embedding' attribute (single)")
        else:
            print("❌ No embeddings found in batch result")
            
    except Exception as e:
        print(f"❌ Batch embedding failed: {e}")
        print("Trying individual embeddings instead...")
        
        # Try individual approach
        try:
            embeddings = []
            for text in texts:
                result = genai.embed_content(
                    model="models/text-embedding-004",
                    content=text,
                    task_type="RETRIEVAL_DOCUMENT"
                )
                if hasattr(result, 'embedding') and result.embedding:
                    embeddings.append(result.embedding)
            
            print(f"✅ Individual embeddings successful: {len(embeddings)} embeddings of {len(embeddings[0])} dimensions each")
            
        except Exception as e2:
            print(f"❌ Individual embeddings also failed: {e2}")


def test_model_availability():
    """Test which embedding models are available."""
    print("\n🔍 Testing model availability...")
    
    models_to_test = [
        "models/text-embedding-004",
        "models/text-embedding-003",
        "models/embedding-001"
    ]
    
    for model in models_to_test:
        try:
            result = genai.embed_content(
                model=model,
                content="Test",
                task_type="RETRIEVAL_DOCUMENT"
            )
            print(f"✅ {model} - Available")
        except Exception as e:
            print(f"❌ {model} - Error: {e}")


if __name__ == "__main__":
    print("🚀 Google Embeddings API Debug Script")
    print("=" * 50)
    
    test_google_embeddings()
    test_model_availability()
    
    print("\n" + "=" * 50)
    print("Debug completed!")
