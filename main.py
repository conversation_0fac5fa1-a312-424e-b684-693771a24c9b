"""
SKUAST RAG Chatbot - FastAPI Application
"""

import os
import logging
import tempfile
import shutil
import uuid
import asyncio
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager

from fastapi import FastAPI, HTTPException, BackgroundTasks, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from dotenv import load_dotenv

from src.pdf_processor import PDFProcessor
from src.text_chunker import TextChunker
from src.vector_db import VectorDatabase
from src.retriever import SemanticRetriever
from src.gemini_client import GeminiClient
from src.config import Config

# Load environment variables
load_dotenv()

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global variables for components
vector_db: Optional[VectorDatabase] = None
retriever: Optional[SemanticRetriever] = None
gemini_client: Optional[GeminiClient] = None
system_initialized = False

# Global dictionary to track temporary databases
# Format: {session_id: {"vector_db": VectorDatabase, "retriever": SemanticRetriever, "created_at": datetime, "path": str}}
temp_databases: Dict[str, Dict[str, Any]] = {}


# Pydantic models
class ChatRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="User query about SKUAST")


class ChatResponse(BaseModel):
    response: str = Field(..., description="AI-generated response")


class SystemStatus(BaseModel):
    status: str = Field(..., description="Overall system status")
    components: Dict[str, Any] = Field(..., description="Status of individual components")
    statistics: Dict[str, Any] = Field(default={}, description="System statistics")


class UploadResponse(BaseModel):
    session_id: str = Field(..., description="Unique session ID for the temporary database")
    files_processed: int = Field(..., description="Number of PDF files successfully processed")
    total_chunks: int = Field(..., description="Total number of text chunks created")
    processing_errors: List[str] = Field(default=[], description="List of processing errors for failed files")
    database_path: str = Field(..., description="Path to the temporary vector database")
    index_stats: Dict[str, Any] = Field(default={}, description="Statistics about the created index")


class QueryTempRequest(BaseModel):
    query: str = Field(..., min_length=1, max_length=1000, description="User query for the temporary database")


class QueryTempResponse(BaseModel):
    response: str = Field(..., description="AI-generated response from temporary database")


class CleanupResponse(BaseModel):
    message: str = Field(..., description="Cleanup operation result message")
    deleted_sessions: List[str] = Field(default=[], description="List of deleted session IDs")


async def initialize_system():
    """Initialize all system components with comprehensive error handling."""
    global vector_db, retriever, gemini_client, system_initialized

    try:
        logger.info("Initializing SKUAST RAG system...")

        # Validate configuration first with error handling
        try:
            config_validation = Config.validate_config()
            if not config_validation['valid']:
                logger.error("Configuration validation failed:")
                for issue in config_validation['issues']:
                    logger.error(f"  - {issue}")
                raise ValueError("Invalid configuration")

            # Log warnings
            for warning in config_validation['warnings']:
                logger.warning(warning)
        except Exception as e:
            logger.error(f"Error during configuration validation: {e}")
            raise ValueError(f"Configuration validation error: {e}")

        # Initialize vector database with error handling
        try:
            logger.info("Initializing vector database...")
            vector_db_config = Config.get_vector_db_config()

            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            vector_db = VectorDatabase(
                model_name=vector_db_config['model_name'],
                db_path=vector_db_config['db_path'],
                api_key=Config.GOOGLE_API_KEY
            )
        except Exception as e:
            logger.error(f"Error initializing vector database: {e}")
            raise ValueError(f"Vector database initialization failed: {e}")

        # Try to load existing index with error handling
        try:
            if vector_db.load_index(Config.INDEX_NAME):
                logger.info("Loaded existing FAISS index")
            else:
                logger.info("No existing index found. Will need to process PDF first.")
        except Exception as e:
            logger.warning(f"Error loading existing index: {e}")
            logger.info("Will need to process PDF to create new index.")

        # Initialize retriever with error handling
        try:
            logger.info("Initializing semantic retriever...")
            retriever = SemanticRetriever(vector_db)
        except Exception as e:
            logger.error(f"Error initializing semantic retriever: {e}")
            raise ValueError(f"Semantic retriever initialization failed: {e}")

        # Initialize Gemini client with error handling
        try:
            logger.info("Initializing Gemini client...")
            if not Config.GOOGLE_API_KEY:
                raise ValueError("Google API key not configured")

            gemini_client = GeminiClient(api_key=Config.GOOGLE_API_KEY, model_name=Config.GEMINI_MODEL)

            # Validate Gemini connection
            if not gemini_client.validate_api_key():
                raise ValueError("Failed to validate Google API key")
        except Exception as e:
            logger.error(f"Error initializing Gemini client: {e}")
            raise ValueError(f"Gemini client initialization failed: {e}")
        
        system_initialized = True
        logger.info("System initialization completed successfully")
        
    except Exception as e:
        logger.error(f"System initialization failed: {e}")
        system_initialized = False
        raise


def status_callback(message: str, level: str = "info"):
    """Status callback function for processing updates."""
    if level == "error":
        logger.error(message)
    elif level == "warning":
        logger.warning(message)
    elif level == "success":
        logger.info(f"SUCCESS: {message}")
    else:
        logger.info(message)


def validate_pdf_file(file: UploadFile) -> Dict[str, Any]:
    """
    Validate uploaded PDF file.

    Args:
        file (UploadFile): Uploaded file to validate

    Returns:
        Dict[str, Any]: Validation result with 'valid' boolean and 'error' message if invalid
    """
    # Check file extension
    if not file.filename or not file.filename.lower().endswith('.pdf'):
        return {"valid": False, "error": f"File '{file.filename}' is not a PDF file"}

    # Check MIME type
    if file.content_type and not file.content_type.lower().startswith('application/pdf'):
        return {"valid": False, "error": f"File '{file.filename}' has invalid MIME type: {file.content_type}"}

    # Check file size (10MB limit)
    max_size = 10 * 1024 * 1024  # 10MB in bytes
    if hasattr(file, 'size') and file.size and file.size > max_size:
        return {"valid": False, "error": f"File '{file.filename}' exceeds 10MB size limit"}

    return {"valid": True, "error": None}


def generate_session_id() -> str:
    """Generate a unique session ID."""
    return f"session_{uuid.uuid4().hex[:12]}_{int(datetime.now().timestamp())}"


def get_temp_db_path(session_id: str) -> str:
    """Get the path for a temporary database."""
    return f"data/temp_uploads/{session_id}"


async def cleanup_old_temp_databases(max_age_hours: int = 1):
    """
    Clean up temporary databases older than specified hours.

    Args:
        max_age_hours (int): Maximum age in hours before cleanup
    """
    global temp_databases

    cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
    sessions_to_remove = []

    for session_id, db_info in temp_databases.items():
        if db_info["created_at"] < cutoff_time:
            sessions_to_remove.append(session_id)

    for session_id in sessions_to_remove:
        try:
            # Remove from memory
            del temp_databases[session_id]

            # Remove files
            db_path = get_temp_db_path(session_id)
            if Path(db_path).exists():
                shutil.rmtree(db_path)
                logger.info(f"Cleaned up temporary database: {session_id}")
        except Exception as e:
            logger.error(f"Error cleaning up session {session_id}: {e}")


async def process_uploaded_pdfs(files: List[UploadFile], session_id: str) -> Dict[str, Any]:
    """
    Process uploaded PDF files and create temporary vector database.

    Args:
        files (List[UploadFile]): List of uploaded PDF files
        session_id (str): Unique session identifier

    Returns:
        Dict[str, Any]: Processing results
    """
    temp_dir = Path(get_temp_db_path(session_id))
    temp_dir.mkdir(parents=True, exist_ok=True)

    pdf_processor = PDFProcessor()
    chunking_config = Config.get_chunking_config()
    chunker = TextChunker(
        chunk_size=chunking_config['chunk_size'],
        overlap_size=chunking_config['overlap_size']
    )

    all_chunks = []
    processing_errors = []
    files_processed = 0

    for file in files:
        try:
            # Save uploaded file temporarily
            temp_file_path = temp_dir / f"temp_{file.filename}"

            with open(temp_file_path, "wb") as temp_file:
                content = await file.read()
                temp_file.write(content)

            # Process PDF
            try:
                markdown_content = pdf_processor.process_pdf(str(temp_file_path))

                if markdown_content and len(markdown_content.strip()) >= 100:
                    # Chunk the content
                    chunks = chunker.split_text_into_chunks(markdown_content)

                    # Add file source to chunk metadata
                    for chunk in chunks:
                        chunk['source_file'] = file.filename
                        chunk['session_id'] = session_id

                    all_chunks.extend(chunks)
                    files_processed += 1
                    logger.info(f"Successfully processed {file.filename}: {len(chunks)} chunks")
                else:
                    processing_errors.append(f"File '{file.filename}': Insufficient content extracted")

            except Exception as e:
                processing_errors.append(f"File '{file.filename}': {str(e)}")
                logger.error(f"Error processing {file.filename}: {e}")

            # Clean up temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()

        except Exception as e:
            processing_errors.append(f"File '{file.filename}': Failed to save/read file - {str(e)}")
            logger.error(f"Error handling file {file.filename}: {e}")

    return {
        "all_chunks": all_chunks,
        "files_processed": files_processed,
        "processing_errors": processing_errors
    }





async def process_pdf_if_needed():
    """Process PDF and build index if not already done with comprehensive error handling."""
    global vector_db

    try:
        # Check if processing is needed
        if not vector_db:
            logger.error("Vector database not initialized")
            return

        if vector_db.index is not None:
            logger.info("Index already exists, skipping PDF processing")
            return  # Index already exists

        # Check if PDF file exists
        pdf_path = "skuast.pdf"
        if not Path(pdf_path).exists():
            error_msg = f"PDF file not found: {pdf_path}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        status_callback("Processing PDF and building vector index...", "info")

        # Process PDF with error handling
        try:
            pdf_processor = PDFProcessor()
            markdown_content = pdf_processor.process_pdf(pdf_path)
        except Exception as e:
            error_msg = f"Error processing PDF file: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate PDF content
        if not markdown_content or len(markdown_content.strip()) < 100:
            status_callback("PDF processing resulted in insufficient content", "warning")
            return

        # Chunk text with error handling
        try:
            chunking_config = Config.get_chunking_config()
            chunker = TextChunker(
                chunk_size=chunking_config['chunk_size'],
                overlap_size=chunking_config['overlap_size']
            )
            chunks = chunker.split_text_into_chunks(markdown_content)
        except Exception as e:
            error_msg = f"Error chunking text: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Validate chunks
        if not chunks:
            status_callback("Text chunking resulted in no chunks", "error")
            return

        # Build vector index with error handling
        try:
            build_success = vector_db.build_index(chunks, status_callback=status_callback)
        except Exception as e:
            error_msg = f"Error building vector index: {e}"
            logger.error(error_msg)
            status_callback(error_msg, "error")
            return

        # Save index with error handling
        if build_success:
            try:
                vector_db.save_index("skuast_index")
                status_callback(f"Successfully processed PDF and created index with {len(chunks)} chunks", "success")
            except Exception as e:
                error_msg = f"Error saving index: {e}"
                logger.error(error_msg)
                status_callback(error_msg, "error")
        else:
            status_callback("Failed to build vector index", "error")

    except Exception as e:
        error_msg = f"Unexpected error processing PDF: {e}"
        logger.error(error_msg)
        status_callback(error_msg, "error")


async def periodic_cleanup():
    """Periodic cleanup task for temporary databases."""
    while True:
        try:
            await cleanup_old_temp_databases(max_age_hours=1)
            await asyncio.sleep(1800)  # Run every 30 minutes
        except Exception as e:
            logger.error(f"Error in periodic cleanup: {e}")
            await asyncio.sleep(1800)  # Continue running even if there's an error


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Manage application lifespan."""
    # Startup
    await initialize_system()
    await process_pdf_if_needed()

    # Start periodic cleanup task
    cleanup_task = asyncio.create_task(periodic_cleanup())

    yield

    # Shutdown
    logger.info("Shutting down SKUAST RAG system")

    # Cancel cleanup task
    cleanup_task.cancel()
    try:
        await cleanup_task
    except asyncio.CancelledError:
        pass

    # Clean up all temporary databases on shutdown
    try:
        global temp_databases
        for session_id in list(temp_databases.keys()):
            try:
                del temp_databases[session_id]
                db_path = get_temp_db_path(session_id)
                if Path(db_path).exists():
                    shutil.rmtree(db_path)
                logger.info(f"Cleaned up session {session_id} on shutdown")
            except Exception as e:
                logger.error(f"Error cleaning up session {session_id} on shutdown: {e}")
    except Exception as e:
        logger.error(f"Error during shutdown cleanup: {e}")


# Create FastAPI app
app = FastAPI(
    title="SKUAST RAG Chatbot",
    description="A Retrieval-Augmented Generation chatbot for SKUAST (Sher-e-Kashmir University of Agricultural Sciences and Technology)",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# @app.get("/", response_model=Dict[str, str])
# async def root():
#     """Root endpoint with basic information."""
#     return {
#         "message": "SKUAST RAG Chatbot API",
#         "description": "Ask questions about Sher-e-Kashmir University of Agricultural Sciences and Technology",
#         "endpoints": {
#             "chat": "POST /chat - Ask questions about SKUAST",
#             "status": "GET /status - Check system status",
#             "health": "GET /health - Health check"
#         }
#     }


@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """
    Chat endpoint for asking questions about SKUAST.
    """
    try:
        # System initialization check
        if not system_initialized:
            logger.error("Chat request received but system not initialized")
            raise HTTPException(
                status_code=503,
                detail="System is not ready. Please try again in a moment."
            )

        # Component availability check
        if not all([vector_db, retriever, gemini_client]):
            logger.error("Chat request received but system components not available")
            raise HTTPException(
                status_code=503,
                detail="System components are not available. Please try again later."
            )

        # Input validation
        if not request.query or not request.query.strip():
            logger.warning("Empty query received")
            raise HTTPException(
                status_code=400,
                detail="Query cannot be empty"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in initial chat validation: {e}")
        raise HTTPException(
            status_code=500,
            detail="Internal server error during request validation"
        )
    
    # Main processing with comprehensive error handling
    try:
        logger.info(f"Processing chat request: '{request.query[:50]}...'")

        # Use hardcoded values for top_k and temperature
        top_k = 3  # Hardcoded default value
        temperature = 0.3  # Hardcoded default value

        # Create context using retriever with error handling
        try:
            context_data = retriever.create_context_for_llm(
                query=request.query,
                top_k=top_k
            )
        except Exception as e:
            logger.error(f"Error during context retrieval: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving relevant information. Please try again."
            )

        # Check if context was found
        if not context_data or context_data.get('num_sources', 0) == 0:
            logger.info("No relevant context found for query")
            return ChatResponse(
                response="Sorry, I don't have information on this topic."
            )
        
        # Generate response using Gemini with comprehensive error handling
        try:
            logger.info("Generating response using Gemini")
            gemini_response = gemini_client.generate_response(
                query=request.query,
                context=context_data['context'],
                temperature=temperature
            )

            # Check if response generation was successful
            if not gemini_response or not gemini_response.get('success', False):
                error_msg = gemini_response.get('error', 'Unknown error') if gemini_response else 'No response received'
                logger.warning(f"Gemini generation failed: {error_msg}")
                return ChatResponse(
                    response="Sorry, I couldn't generate a response at the moment. Please try again."
                )

            # Validate response content
            if not gemini_response.get('response') or not gemini_response['response'].strip():
                logger.warning("Gemini returned empty response")
                return ChatResponse(
                    response="Sorry, I couldn't generate a meaningful response. Please try rephrasing your question."
                )

        except ConnectionError as e:
            logger.error(f"Network connection error during Gemini API call: {e}")
            raise HTTPException(
                status_code=503,
                detail="Service temporarily unavailable. Please try again later."
            )
        except TimeoutError as e:
            logger.error(f"Timeout error during Gemini API call: {e}")
            raise HTTPException(
                status_code=504,
                detail="Request timeout. Please try again."
            )
        except Exception as e:
            logger.error(f"Unexpected error during response generation: {e}")
            return ChatResponse(
                response="Sorry, I encountered an error while processing your question. Please try again."
            )
        
        # Successful response
        return ChatResponse(
            response=gemini_response['response']
        )

    except HTTPException:
        # Re-raise HTTP exceptions (they're already handled)
        raise
    except ValueError as e:
        logger.error(f"Validation error in chat processing: {e}")
        raise HTTPException(
            status_code=400,
            detail="Invalid request data"
        )
    except ConnectionError as e:
        logger.error(f"Connection error in chat processing: {e}")
        raise HTTPException(
            status_code=503,
            detail="Service temporarily unavailable"
        )
    except TimeoutError as e:
        logger.error(f"Timeout error in chat processing: {e}")
        raise HTTPException(
            status_code=504,
            detail="Request timeout"
        )
    except Exception as e:
        logger.error(f"Unexpected error processing chat request: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred. Please try again."
        )


@app.post("/upload-pdfs", response_model=UploadResponse)
async def upload_pdfs(
    background_tasks: BackgroundTasks,
    files: List[UploadFile] = File(..., description="PDF files to upload and process")
):
    """
    Upload multiple PDF files and process them into a temporary vector database.
    """
    try:
        # Validate number of files
        if len(files) > 5:
            raise HTTPException(
                status_code=400,
                detail="Maximum 5 files allowed per upload"
            )

        if len(files) == 0:
            raise HTTPException(
                status_code=400,
                detail="At least one file must be uploaded"
            )

        # Validate each file
        validation_errors = []
        for file in files:
            validation_result = validate_pdf_file(file)
            if not validation_result["valid"]:
                validation_errors.append(validation_result["error"])

        if validation_errors:
            raise HTTPException(
                status_code=400,
                detail=f"File validation failed: {'; '.join(validation_errors)}"
            )

        # Generate session ID
        session_id = generate_session_id()

        # Process uploaded PDFs
        try:
            processing_result = await process_uploaded_pdfs(files, session_id)

            if processing_result["files_processed"] == 0:
                raise HTTPException(
                    status_code=400,
                    detail=f"No files could be processed. Errors: {'; '.join(processing_result['processing_errors'])}"
                )

            # Create temporary vector database
            temp_db_path = get_temp_db_path(session_id)
            temp_vector_db = VectorDatabase(
                model_name=Config.EMBEDDING_MODEL,
                db_path=temp_db_path,
                api_key=Config.GOOGLE_API_KEY
            )

            # Build index from chunks
            chunks = processing_result["all_chunks"]
            build_success = temp_vector_db.build_index(chunks)

            if not build_success:
                raise HTTPException(
                    status_code=500,
                    detail="Failed to build vector index from uploaded files"
                )

            # Save the temporary index
            temp_vector_db.save_index(f"temp_index_{session_id}")

            # Create temporary retriever
            temp_retriever = SemanticRetriever(temp_vector_db)

            # Store in global dictionary
            temp_databases[session_id] = {
                "vector_db": temp_vector_db,
                "retriever": temp_retriever,
                "created_at": datetime.now(),
                "path": temp_db_path,
                "files_processed": processing_result["files_processed"],
                "total_chunks": len(chunks)
            }

            # Schedule cleanup task
            background_tasks.add_task(cleanup_old_temp_databases)

            # Get index statistics
            index_stats = temp_vector_db.get_index_stats()

            logger.info(f"Successfully created temporary database {session_id} with {len(chunks)} chunks")

            return UploadResponse(
                session_id=session_id,
                files_processed=processing_result["files_processed"],
                total_chunks=len(chunks),
                processing_errors=processing_result["processing_errors"],
                database_path=temp_db_path,
                index_stats=index_stats
            )

        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Error processing uploaded files: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"Error processing uploaded files: {str(e)}"
            )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Unexpected error in upload endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred during file upload"
        )


@app.post("/query-temp-db/{session_id}", response_model=QueryTempResponse)
async def query_temp_database(session_id: str, request: QueryTempRequest):
    """
    Query a temporary vector database created from uploaded PDFs.
    """
    try:
        # Check if session exists
        if session_id not in temp_databases:
            raise HTTPException(
                status_code=404,
                detail=f"Session '{session_id}' not found or has expired"
            )

        # Get temporary database components
        temp_db_info = temp_databases[session_id]
        temp_retriever = temp_db_info["retriever"]

        # Input validation
        if not request.query or not request.query.strip():
            raise HTTPException(
                status_code=400,
                detail="Query cannot be empty"
            )

        # Use hardcoded values for top_k and temperature
        top_k = 3
        temperature = 0.3

        # Create context using temporary retriever
        try:
            context_data = temp_retriever.create_context_for_llm(
                query=request.query,
                top_k=top_k
            )
        except Exception as e:
            logger.error(f"Error during context retrieval from temp DB {session_id}: {e}")
            raise HTTPException(
                status_code=500,
                detail="Error retrieving relevant information from uploaded documents"
            )

        # Check if context was found
        if not context_data or context_data.get('num_sources', 0) == 0:
            return QueryTempResponse(
                response="Sorry, I don't have information on this topic in the uploaded documents."
            )

        # Generate response using Gemini
        try:
            if not gemini_client:
                raise HTTPException(
                    status_code=503,
                    detail="AI service not available"
                )

            gemini_response = gemini_client.generate_response(
                query=request.query,
                context=context_data['context'],
                temperature=temperature
            )

            # Check if response generation was successful
            if not gemini_response or not gemini_response.get('success', False):
                return QueryTempResponse(
                    response="Sorry, I couldn't generate a response at the moment. Please try again."
                )

            # Validate response content
            if not gemini_response.get('response') or not gemini_response['response'].strip():
                return QueryTempResponse(
                    response="Sorry, I couldn't generate a meaningful response. Please try rephrasing your question."
                )

            logger.info(f"Successfully processed query for temp DB {session_id}")

            return QueryTempResponse(
                response=gemini_response['response']
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating response for temp DB {session_id}: {e}")
            return QueryTempResponse(
                response="Sorry, I encountered an error while processing your question. Please try again."
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Unexpected error in temp DB query endpoint: {e}")
        raise HTTPException(
            status_code=500,
            detail="An unexpected error occurred while processing your query"
        )


@app.delete("/clear-temp-db/{session_id}", response_model=CleanupResponse)
async def clear_temp_database(session_id: str):
    """
    Manually delete a specific temporary database.
    """
    try:
        if session_id not in temp_databases:
            raise HTTPException(
                status_code=404,
                detail=f"Session '{session_id}' not found"
            )

        # Remove from memory
        del temp_databases[session_id]

        # Remove files
        db_path = get_temp_db_path(session_id)
        if Path(db_path).exists():
            shutil.rmtree(db_path)

        logger.info(f"Successfully deleted temporary database: {session_id}")

        return CleanupResponse(
            message=f"Successfully deleted temporary database '{session_id}'",
            deleted_sessions=[session_id]
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting temp database {session_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error deleting temporary database: {str(e)}"
        )


@app.post("/cleanup-temp-dbs", response_model=CleanupResponse)
async def cleanup_temp_databases_endpoint(max_age_hours: int = 1):
    """
    Manually trigger cleanup of old temporary databases.
    """
    try:
        global temp_databases

        cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
        sessions_to_remove = []

        for session_id, db_info in temp_databases.items():
            if db_info["created_at"] < cutoff_time:
                sessions_to_remove.append(session_id)

        deleted_sessions = []
        for session_id in sessions_to_remove:
            try:
                # Remove from memory
                del temp_databases[session_id]

                # Remove files
                db_path = get_temp_db_path(session_id)
                if Path(db_path).exists():
                    shutil.rmtree(db_path)

                deleted_sessions.append(session_id)
                logger.info(f"Cleaned up temporary database: {session_id}")
            except Exception as e:
                logger.error(f"Error cleaning up session {session_id}: {e}")

        return CleanupResponse(
            message=f"Cleaned up {len(deleted_sessions)} temporary databases older than {max_age_hours} hours",
            deleted_sessions=deleted_sessions
        )

    except Exception as e:
        logger.error(f"Error during cleanup: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error during cleanup: {str(e)}"
        )


# @app.get("/status", response_model=SystemStatus)
# async def get_status():
#     """Get system status and component information."""
#     try:
#         components = {}
        
#         # Vector database status
#         if vector_db:
#             components["vector_database"] = vector_db.get_index_stats()
#         else:
#             components["vector_database"] = {"status": "Not initialized"}
        
#         # Retriever status
#         if retriever:
#             components["retriever"] = retriever.get_retrieval_stats()
#         else:
#             components["retriever"] = {"status": "Not initialized"}
        
#         # Gemini client status
#         if gemini_client:
#             components["gemini_client"] = gemini_client.get_model_info()
#         else:
#             components["gemini_client"] = {"status": "Not initialized"}
        
#         # Overall status
#         overall_status = "Ready" if system_initialized else "Not Ready"
        
#         # Statistics
#         statistics = {
#             "system_initialized": system_initialized,
#             "pdf_processed": vector_db.index is not None if vector_db else False,
#             "api_key_configured": os.getenv('GOOGLE_API_KEY') is not None
#         }
        
#         return SystemStatus(
#             status=overall_status,
#             components=components,
#             statistics=statistics
#         )
        
#     except Exception as e:
#         logger.error(f"Error getting system status: {e}")
#         raise HTTPException(
#             status_code=500,
#             detail=f"Error retrieving system status: {str(e)}"
#         )


# @app.get("/health")
# async def health_check():
#     """Simple health check endpoint."""
#     return {
#         "status": "healthy" if system_initialized else "unhealthy",
#         "timestamp": "2024-01-01T00:00:00Z"  # You might want to use actual timestamp
#     }


# @app.post("/rebuild-index")
# async def rebuild_index(background_tasks: BackgroundTasks):
    """Rebuild the vector index (admin endpoint)."""
    if not system_initialized:
        raise HTTPException(
            status_code=503,
            detail="System not initialized"
        )
    
    background_tasks.add_task(process_pdf_if_needed)
    
    return {
        "message": "Index rebuild started in background",
        "status": "processing"
    }


if __name__ == "__main__":
    import uvicorn

    # Get API configuration
    api_config = Config.get_api_config()

    # Run the application
    uvicorn.run(
        "main:app",
        host=api_config['host'],
        port=api_config['port'],
        reload=api_config['reload'],
        log_level="info"
    )
