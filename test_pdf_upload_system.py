#!/usr/bin/env python3
"""
Test script for the PDF upload and temporary database system.
"""

import requests
import json
import time
import tempfile
import os
from pathlib import Path


def create_test_pdf(content: str, filename: str) -> str:
    """
    Create a simple test PDF file for testing.
    Note: This creates a text file with .pdf extension for testing purposes.
    In a real scenario, you'd use a proper PDF library.
    """
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, filename)
    
    with open(file_path, 'w') as f:
        f.write(content)
    
    return file_path


def test_pdf_upload_system(base_url="http://localhost:8000"):
    """Test the PDF upload and temporary database system."""
    
    print("🚀 PDF Upload System Test")
    print("=" * 60)
    
    # Test 1: Upload PDF files
    print("📤 Test 1: Uploading PDF files...")
    
    # Create test PDF files (note: these are text files with .pdf extension for testing)
    test_files = [
        ("test_doc1.pdf", "This is a test document about agriculture. It contains information about farming techniques and crop management."),
        ("test_doc2.pdf", "This document discusses sustainable farming practices and organic agriculture methods.")
    ]
    
    temp_files = []
    files_to_upload = []
    
    try:
        # Create temporary test files
        for filename, content in test_files:
            file_path = create_test_pdf(content, filename)
            temp_files.append(file_path)
            
            # Prepare for upload
            files_to_upload.append(
                ('files', (filename, open(file_path, 'rb'), 'application/pdf'))
            )
        
        # Upload files
        response = requests.post(f"{base_url}/upload-pdfs", files=files_to_upload)
        
        # Close file handles
        for _, (_, file_handle, _) in files_to_upload:
            file_handle.close()
        
        if response.status_code == 200:
            upload_result = response.json()
            session_id = upload_result['session_id']
            
            print("✅ Upload successful!")
            print(f"   Session ID: {session_id}")
            print(f"   Files processed: {upload_result['files_processed']}")
            print(f"   Total chunks: {upload_result['total_chunks']}")
            print(f"   Database path: {upload_result['database_path']}")
            
            if upload_result['processing_errors']:
                print(f"   Processing errors: {upload_result['processing_errors']}")
            
            # Test 2: Query the temporary database
            print(f"\n🔍 Test 2: Querying temporary database {session_id}...")
            
            test_queries = [
                "What is this document about?",
                "Tell me about agriculture",
                "What farming techniques are mentioned?",
                "What information is available about organic farming?"
            ]
            
            for query in test_queries:
                print(f"\n   Query: {query}")
                
                query_response = requests.post(
                    f"{base_url}/query-temp-db/{session_id}",
                    json={"query": query}
                )
                
                if query_response.status_code == 200:
                    result = query_response.json()
                    print(f"   Response: {result['response']}")
                else:
                    print(f"   ❌ Query failed: {query_response.status_code} - {query_response.text}")
            
            # Test 3: Test session management
            print(f"\n🗂️ Test 3: Session management...")
            
            # Test querying non-existent session
            fake_session = "session_nonexistent_123"
            fake_response = requests.post(
                f"{base_url}/query-temp-db/{fake_session}",
                json={"query": "test"}
            )
            
            if fake_response.status_code == 404:
                print("✅ Non-existent session correctly returns 404")
            else:
                print(f"❌ Expected 404 for non-existent session, got {fake_response.status_code}")
            
            # Test 4: Cleanup
            print(f"\n🧹 Test 4: Cleanup...")
            
            # Manual cleanup of the test session
            cleanup_response = requests.delete(f"{base_url}/clear-temp-db/{session_id}")
            
            if cleanup_response.status_code == 200:
                cleanup_result = cleanup_response.json()
                print("✅ Manual cleanup successful!")
                print(f"   Message: {cleanup_result['message']}")
                print(f"   Deleted sessions: {cleanup_result['deleted_sessions']}")
                
                # Verify session is gone
                verify_response = requests.post(
                    f"{base_url}/query-temp-db/{session_id}",
                    json={"query": "test"}
                )
                
                if verify_response.status_code == 404:
                    print("✅ Session successfully deleted (verified)")
                else:
                    print(f"❌ Session still exists after deletion: {verify_response.status_code}")
            else:
                print(f"❌ Cleanup failed: {cleanup_response.status_code} - {cleanup_response.text}")
            
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False
    
    finally:
        # Clean up temporary files
        for file_path in temp_files:
            try:
                os.unlink(file_path)
                # Also remove the temporary directory
                os.rmdir(os.path.dirname(file_path))
            except Exception as e:
                print(f"Warning: Could not clean up temp file {file_path}: {e}")
    
    return True


def test_error_scenarios(base_url="http://localhost:8000"):
    """Test error handling scenarios."""
    
    print("\n⚠️ Testing Error Scenarios")
    print("=" * 60)
    
    # Test 1: Upload non-PDF file
    print("📤 Test 1: Upload non-PDF file...")
    
    temp_dir = tempfile.mkdtemp()
    txt_file = os.path.join(temp_dir, "test.txt")
    
    try:
        with open(txt_file, 'w') as f:
            f.write("This is not a PDF file")
        
        with open(txt_file, 'rb') as f:
            files = [('files', ('test.txt', f, 'text/plain'))]
            response = requests.post(f"{base_url}/upload-pdfs", files=files)
        
        if response.status_code == 400:
            print("✅ Non-PDF file correctly rejected")
        else:
            print(f"❌ Expected 400 for non-PDF file, got {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error testing non-PDF upload: {e}")
    
    finally:
        try:
            os.unlink(txt_file)
            os.rmdir(temp_dir)
        except:
            pass
    
    # Test 2: Upload too many files
    print("\n📤 Test 2: Upload too many files...")
    
    try:
        # Create 6 files (exceeds limit of 5)
        files = []
        temp_files = []
        
        for i in range(6):
            temp_dir = tempfile.mkdtemp()
            file_path = os.path.join(temp_dir, f"test{i}.pdf")
            temp_files.append(file_path)
            
            with open(file_path, 'w') as f:
                f.write(f"Test content {i}")
            
            files.append(('files', (f'test{i}.pdf', open(file_path, 'rb'), 'application/pdf')))
        
        response = requests.post(f"{base_url}/upload-pdfs", files=files)
        
        # Close file handles
        for _, (_, file_handle, _) in files:
            file_handle.close()
        
        if response.status_code == 400 and "Maximum 5 files" in response.text:
            print("✅ Too many files correctly rejected")
        else:
            print(f"❌ Expected 400 for too many files, got {response.status_code}")
    
    except Exception as e:
        print(f"❌ Error testing too many files: {e}")
    
    finally:
        for file_path in temp_files:
            try:
                os.unlink(file_path)
                os.rmdir(os.path.dirname(file_path))
            except:
                pass
    
    # Test 3: Empty query
    print("\n🔍 Test 3: Empty query...")
    
    # First create a valid session
    temp_dir = tempfile.mkdtemp()
    file_path = os.path.join(temp_dir, "test.pdf")
    
    try:
        with open(file_path, 'w') as f:
            f.write("Test content for empty query test")
        
        with open(file_path, 'rb') as f:
            files = [('files', ('test.pdf', f, 'application/pdf'))]
            upload_response = requests.post(f"{base_url}/upload-pdfs", files=files)
        
        if upload_response.status_code == 200:
            session_id = upload_response.json()['session_id']
            
            # Test empty query
            query_response = requests.post(
                f"{base_url}/query-temp-db/{session_id}",
                json={"query": ""}
            )
            
            if query_response.status_code == 400:
                print("✅ Empty query correctly rejected")
            else:
                print(f"❌ Expected 400 for empty query, got {query_response.status_code}")
            
            # Clean up
            requests.delete(f"{base_url}/clear-temp-db/{session_id}")
        
    except Exception as e:
        print(f"❌ Error testing empty query: {e}")
    
    finally:
        try:
            os.unlink(file_path)
            os.rmdir(temp_dir)
        except:
            pass


def main():
    """Main test function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Test the PDF upload system")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--test-errors", action="store_true",
                       help="Test error scenarios")
    
    args = parser.parse_args()
    
    # Check if server is running
    try:
        response = requests.get(f"{args.url}/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not accessible at {args.url}")
            print("Make sure the server is running: python main.py")
            return 1
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to server at {args.url}")
        print("Make sure the server is running: python main.py")
        return 1
    
    # Run tests
    success = test_pdf_upload_system(args.url)
    
    if args.test_errors:
        test_error_scenarios(args.url)
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 PDF upload system tests completed successfully!")
    else:
        print("❌ Some tests failed.")
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
