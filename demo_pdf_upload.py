#!/usr/bin/env python3
"""
Demo script for the PDF upload and temporary database functionality.
"""

import requests
import json
import time
from pathlib import Path


def demo_pdf_upload_workflow(base_url="http://localhost:8000"):
    """Demonstrate the complete PDF upload workflow."""
    
    print("🚀 PDF Upload and Temporary Database Demo")
    print("=" * 70)
    print("This demo shows how to upload PDF files and query them using")
    print("temporary vector databases with the SKUAST RAG system.")
    print("=" * 70)
    
    # Step 1: Check if we have any PDF files to upload
    print("📁 Step 1: Looking for PDF files to upload...")
    
    # Look for PDF files in the current directory
    pdf_files = list(Path(".").glob("*.pdf"))
    
    if not pdf_files:
        print("❌ No PDF files found in current directory.")
        print("   Please place some PDF files in the current directory and try again.")
        print("   For testing, you can use the main SKUAST PDF: skuast.pdf")
        return False
    
    # Limit to first 3 files to stay within the 5-file limit
    pdf_files = pdf_files[:3]
    
    print(f"✅ Found {len(pdf_files)} PDF files:")
    for pdf_file in pdf_files:
        file_size = pdf_file.stat().st_size / (1024 * 1024)  # Size in MB
        print(f"   - {pdf_file.name} ({file_size:.2f} MB)")
    
    # Step 2: Upload PDF files
    print(f"\n📤 Step 2: Uploading {len(pdf_files)} PDF files...")
    
    try:
        # Prepare files for upload
        files_to_upload = []
        file_handles = []
        
        for pdf_file in pdf_files:
            file_handle = open(pdf_file, 'rb')
            file_handles.append(file_handle)
            files_to_upload.append(
                ('files', (pdf_file.name, file_handle, 'application/pdf'))
            )
        
        # Upload files
        print("   Uploading files... (this may take a while for large files)")
        response = requests.post(f"{base_url}/upload-pdfs", files=files_to_upload, timeout=300)
        
        # Close file handles
        for file_handle in file_handles:
            file_handle.close()
        
        if response.status_code == 200:
            upload_result = response.json()
            session_id = upload_result['session_id']
            
            print("✅ Upload successful!")
            print(f"   📋 Session ID: {session_id}")
            print(f"   📄 Files processed: {upload_result['files_processed']}")
            print(f"   🧩 Total chunks created: {upload_result['total_chunks']}")
            print(f"   📊 Index stats: {upload_result['index_stats']}")
            
            if upload_result['processing_errors']:
                print(f"   ⚠️  Processing errors:")
                for error in upload_result['processing_errors']:
                    print(f"      - {error}")
            
        else:
            print(f"❌ Upload failed: {response.status_code}")
            print(f"   Error: {response.text}")
            return False
    
    except requests.exceptions.Timeout:
        print("❌ Upload timed out. The files might be too large or the server is busy.")
        return False
    except Exception as e:
        print(f"❌ Upload failed with error: {e}")
        return False
    
    # Step 3: Query the temporary database
    print(f"\n🔍 Step 3: Querying the temporary database...")
    
    # Sample queries to demonstrate functionality
    sample_queries = [
        "What is the main topic of these documents?",
        "Summarize the key information from the uploaded files",
        "What specific details are mentioned in the documents?",
        "Are there any important dates or numbers mentioned?",
        "What conclusions can be drawn from the content?"
    ]
    
    print(f"   Running {len(sample_queries)} sample queries...")
    
    for i, query in enumerate(sample_queries, 1):
        print(f"\n   🔍 Query {i}: {query}")
        print("   " + "-" * 50)
        
        try:
            query_response = requests.post(
                f"{base_url}/query-temp-db/{session_id}",
                json={"query": query},
                timeout=60
            )
            
            if query_response.status_code == 200:
                result = query_response.json()
                response_text = result['response']
                
                # Truncate long responses for demo
                if len(response_text) > 200:
                    response_text = response_text[:200] + "..."
                
                print(f"   💬 Response: {response_text}")
            else:
                print(f"   ❌ Query failed: {query_response.status_code} - {query_response.text}")
        
        except requests.exceptions.Timeout:
            print("   ❌ Query timed out")
        except Exception as e:
            print(f"   ❌ Query error: {e}")
        
        time.sleep(1)  # Small delay between queries
    
    # Step 4: Interactive query session
    print(f"\n💬 Step 4: Interactive query session")
    print("   You can now ask your own questions about the uploaded documents.")
    print("   Type 'quit' to exit the interactive session.")
    print("   " + "=" * 50)
    
    while True:
        try:
            user_query = input("\n   Your question: ").strip()
            
            if user_query.lower() in ['quit', 'exit', 'q']:
                break
            
            if not user_query:
                print("   Please enter a question.")
                continue
            
            print("   Processing...")
            
            query_response = requests.post(
                f"{base_url}/query-temp-db/{session_id}",
                json={"query": user_query},
                timeout=60
            )
            
            if query_response.status_code == 200:
                result = query_response.json()
                print(f"   💬 Response: {result['response']}")
            else:
                print(f"   ❌ Query failed: {query_response.status_code}")
        
        except KeyboardInterrupt:
            print("\n   Exiting interactive session...")
            break
        except requests.exceptions.Timeout:
            print("   ❌ Query timed out")
        except Exception as e:
            print(f"   ❌ Query error: {e}")
    
    # Step 5: Cleanup
    print(f"\n🧹 Step 5: Cleanup")
    
    cleanup_choice = input("   Do you want to delete the temporary database? (y/n): ").strip().lower()
    
    if cleanup_choice in ['y', 'yes']:
        try:
            cleanup_response = requests.delete(f"{base_url}/clear-temp-db/{session_id}")
            
            if cleanup_response.status_code == 200:
                cleanup_result = cleanup_response.json()
                print("   ✅ Cleanup successful!")
                print(f"   📋 {cleanup_result['message']}")
            else:
                print(f"   ❌ Cleanup failed: {cleanup_response.status_code}")
        
        except Exception as e:
            print(f"   ❌ Cleanup error: {e}")
    else:
        print(f"   📋 Temporary database preserved with session ID: {session_id}")
        print("   You can query it later or clean it up manually using:")
        print(f"   curl -X DELETE {base_url}/clear-temp-db/{session_id}")
    
    print("\n" + "=" * 70)
    print("🎉 Demo completed successfully!")
    print("\nKey features demonstrated:")
    print("✅ Multi-file PDF upload with validation")
    print("✅ Automatic text extraction and chunking")
    print("✅ Temporary vector database creation")
    print("✅ Natural language querying")
    print("✅ Session management and cleanup")
    
    return True


def show_api_endpoints(base_url="http://localhost:8000"):
    """Show available API endpoints for PDF upload functionality."""
    
    print("📚 PDF Upload API Endpoints")
    print("=" * 50)
    
    endpoints = [
        {
            "method": "POST",
            "path": "/upload-pdfs",
            "description": "Upload multiple PDF files and create temporary database",
            "example": f"curl -X POST -F 'files=@doc1.pdf' -F 'files=@doc2.pdf' {base_url}/upload-pdfs"
        },
        {
            "method": "POST", 
            "path": "/query-temp-db/{session_id}",
            "description": "Query a temporary database",
            "example": f"curl -X POST -H 'Content-Type: application/json' -d '{{\"query\": \"What is this about?\"}}' {base_url}/query-temp-db/session_123"
        },
        {
            "method": "DELETE",
            "path": "/clear-temp-db/{session_id}",
            "description": "Delete a specific temporary database",
            "example": f"curl -X DELETE {base_url}/clear-temp-db/session_123"
        },
        {
            "method": "POST",
            "path": "/cleanup-temp-dbs",
            "description": "Clean up old temporary databases",
            "example": f"curl -X POST {base_url}/cleanup-temp-dbs?max_age_hours=2"
        }
    ]
    
    for endpoint in endpoints:
        print(f"\n{endpoint['method']} {endpoint['path']}")
        print(f"   Description: {endpoint['description']}")
        print(f"   Example: {endpoint['example']}")
    
    print(f"\n📖 API Documentation: {base_url}/docs")


def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo the PDF upload functionality")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--show-endpoints", action="store_true",
                       help="Show API endpoints information")
    
    args = parser.parse_args()
    
    if args.show_endpoints:
        show_api_endpoints(args.url)
        return 0
    
    # Check if server is running
    try:
        response = requests.get(f"{args.url}/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not accessible at {args.url}")
            print("Make sure the server is running: python main.py")
            return 1
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to server at {args.url}")
        print("Make sure the server is running: python main.py")
        return 1
    
    # Run the demo
    success = demo_pdf_upload_workflow(args.url)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
