"""
PDF text extraction and markdown conversion module.
"""

import re
import logging
from pathlib import Path
from typing import Optional
import PyPDF2

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class PDFProcessor:
    """
    A class to handle PDF text extraction and conversion to markdown format.
    """
    
    def __init__(self):
        """Initialize the PDF processor."""
        pass
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """
        Extract text content from a PDF file.
        
        Args:
            pdf_path (str): Path to the PDF file
            
        Returns:
            str: Extracted text content
            
        Raises:
            FileNotFoundError: If the PDF file doesn't exist
            Exception: If there's an error reading the PDF
        """
        pdf_file = Path(pdf_path)
        
        if not pdf_file.exists():
            raise FileNotFoundError(f"PDF file not found: {pdf_path}")
        
        try:
            text_content = ""
            
            with open(pdf_file, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                logger.info(f"Processing PDF with {len(pdf_reader.pages)} pages")
                
                for page_num, page in enumerate(pdf_reader.pages, 1):
                    try:
                        page_text = page.extract_text()
                        if page_text.strip():
                            text_content += f"\n\n--- Page {page_num} ---\n\n"
                            text_content += page_text
                            logger.debug(f"Extracted text from page {page_num}")
                    except Exception as e:
                        logger.warning(f"Error extracting text from page {page_num}: {e}")
                        continue
                        
            logger.info(f"Successfully extracted {len(text_content)} characters from PDF")
            return text_content
            
        except Exception as e:
            logger.error(f"Error reading PDF file: {e}")
            raise Exception(f"Failed to extract text from PDF: {e}")
    
    def clean_text(self, text: str) -> str:
        """
        Clean and normalize extracted text.
        
        Args:
            text (str): Raw extracted text
            
        Returns:
            str: Cleaned text
        """
        if not text:
            return ""
        
        # Remove excessive whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove page markers
        text = re.sub(r'--- Page \d+ ---', '', text)
        
        # Fix common OCR issues
        text = re.sub(r'([a-z])([A-Z])', r'\1 \2', text)  # Add space between lowercase and uppercase
        text = re.sub(r'(\d)([A-Za-z])', r'\1 \2', text)  # Add space between numbers and letters
        text = re.sub(r'([A-Za-z])(\d)', r'\1 \2', text)  # Add space between letters and numbers
        
        # Remove extra spaces
        text = re.sub(r' +', ' ', text)
        
        # Clean up line breaks
        text = text.replace('\n ', '\n').replace(' \n', '\n')
        text = re.sub(r'\n+', '\n', text)
        
        return text.strip()
    
    def convert_to_markdown(self, text: str) -> str:
        """
        Convert cleaned text to markdown format.
        
        Args:
            text (str): Cleaned text content
            
        Returns:
            str: Text formatted as markdown
        """
        if not text:
            return ""
        
        lines = text.split('\n')
        markdown_lines = []
        
        for line in lines:
            line = line.strip()
            if not line:
                markdown_lines.append('')
                continue
            
            # Detect headings (lines that are all caps or start with numbers/bullets)
            if self._is_heading(line):
                # Determine heading level
                if len(line) < 50 and line.isupper():
                    markdown_lines.append(f"## {line.title()}")
                elif re.match(r'^\d+\.?\s+[A-Z]', line):
                    markdown_lines.append(f"### {line}")
                elif re.match(r'^[•\-\*]\s+', line):
                    markdown_lines.append(f"#### {line}")
                else:
                    markdown_lines.append(f"### {line}")
            else:
                # Regular paragraph text
                markdown_lines.append(line)
        
        # Join lines and clean up
        markdown_text = '\n'.join(markdown_lines)
        
        # Clean up excessive line breaks
        markdown_text = re.sub(r'\n{3,}', '\n\n', markdown_text)
        
        return markdown_text.strip()
    
    def _is_heading(self, line: str) -> bool:
        """
        Determine if a line should be treated as a heading.
        
        Args:
            line (str): Text line to check
            
        Returns:
            bool: True if line should be a heading
        """
        line = line.strip()
        
        # Empty lines are not headings
        if not line:
            return False
        
        # Short lines that are all uppercase
        if len(line) < 100 and line.isupper() and len(line.split()) <= 8:
            return True
        
        # Lines starting with numbers (like "1. Introduction")
        if re.match(r'^\d+\.?\s+[A-Z]', line):
            return True
        
        # Lines starting with bullets
        if re.match(r'^[•\-\*]\s+[A-Z]', line):
            return True
        
        # Lines that look like section headers
        if (len(line) < 80 and 
            line[0].isupper() and 
            not line.endswith('.') and 
            len(line.split()) <= 10):
            return True
        
        return False
    
    def process_pdf(self, pdf_path: str, output_path: Optional[str] = None) -> str:
        """
        Complete PDF processing pipeline: extract, clean, and convert to markdown.
        
        Args:
            pdf_path (str): Path to the PDF file
            output_path (str, optional): Path to save the markdown output
            
        Returns:
            str: Processed markdown content
        """
        logger.info(f"Starting PDF processing for: {pdf_path}")
        
        # Extract text from PDF
        raw_text = self.extract_text_from_pdf(pdf_path)
        
        # Clean the text
        cleaned_text = self.clean_text(raw_text)
        
        # Convert to markdown
        markdown_content = self.convert_to_markdown(cleaned_text)
        
        # Save to file if output path is provided
        if output_path:
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"Markdown content saved to: {output_path}")
        
        logger.info("PDF processing completed successfully")
        return markdown_content


def main():
    """
    Main function for testing the PDF processor.
    """
    processor = PDFProcessor()
    
    try:
        # Process the SKUAST PDF
        markdown_content = processor.process_pdf(
            pdf_path="skuast.pdf",
            output_path="data/skuast_content.md"
        )
        
        print(f"Successfully processed PDF. Content length: {len(markdown_content)} characters")
        print("\nFirst 500 characters of processed content:")
        print(markdown_content[:500])
        
    except Exception as e:
        print(f"Error processing PDF: {e}")


if __name__ == "__main__":
    main()
