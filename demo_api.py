#!/usr/bin/env python3
"""
Demo script showing the simplified API usage.
"""

import requests
import json
import time


def test_api_endpoint(base_url="http://localhost:8000"):
    """Test the simplified API endpoint."""
    
    print("🚀 SKUAST RAG API Demo")
    print("=" * 50)
    
    # Test queries
    test_queries = [
        "What is SKUAST?",
        "Tell me about agricultural programs at SKUAST",
        "What are the admission requirements?",
        "Who is the current vice-chancellor?",
        "What research facilities are available?",
        "Tell me about something that doesn't exist in the document"  # Test missing info handling
    ]
    
    print(f"Testing API at: {base_url}")
    print(f"Number of test queries: {len(test_queries)}")
    print()
    
    for i, query in enumerate(test_queries, 1):
        print(f"Query {i}: {query}")
        print("-" * 60)
        
        try:
            # Make API request with simplified schema
            response = requests.post(
                f"{base_url}/chat",
                json={"query": query},  # Only query parameter
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                # Verify simplified response schema
                if len(data) == 1 and 'response' in data:
                    print("✅ Response received (simplified schema)")
                    print(f"Response: {data['response']}")
                else:
                    print("⚠️  Response received but schema is not simplified")
                    print(f"Response keys: {list(data.keys())}")
                    print(f"Response: {data}")
                    
            else:
                print(f"❌ API request failed: {response.status_code}")
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Connection error: {e}")
        
        print()
        time.sleep(1)  # Small delay between requests
    
    print("Demo completed!")


def test_error_scenarios(base_url="http://localhost:8000"):
    """Test error handling scenarios."""
    
    print("🔍 Testing Error Scenarios")
    print("=" * 50)
    
    error_tests = [
        ("Empty query", {"query": ""}),
        ("Missing query", {}),
        ("Very long query", {"query": "x" * 2000}),
        ("Special characters", {"query": "What about 特殊字符 and émojis 🎓?"}),
    ]
    
    for test_name, payload in error_tests:
        print(f"Test: {test_name}")
        print(f"Payload: {payload}")
        
        try:
            response = requests.post(
                f"{base_url}/chat",
                json=payload,
                timeout=10
            )
            
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Response: {data.get('response', 'No response field')}")
            else:
                print(f"Error: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"Connection error: {e}")
        
        print("-" * 40)
        print()


def compare_old_vs_new_schema():
    """Show the difference between old and new API schemas."""
    
    print("📊 API Schema Comparison")
    print("=" * 50)
    
    print("OLD REQUEST SCHEMA:")
    old_request = {
        "query": "What is SKUAST?",
        "top_k": 5,
        "temperature": 0.7
    }
    print(json.dumps(old_request, indent=2))
    
    print("\nNEW REQUEST SCHEMA:")
    new_request = {
        "query": "What is SKUAST?"
    }
    print(json.dumps(new_request, indent=2))
    
    print("\nOLD RESPONSE SCHEMA:")
    old_response = {
        "response": "SKUAST is...",
        "success": True,
        "sources": ["source1", "source2"],
        "metadata": {"num_sources": 2, "query": "What is SKUAST?"},
        "error": None
    }
    print(json.dumps(old_response, indent=2))
    
    print("\nNEW RESPONSE SCHEMA:")
    new_response = {
        "response": "SKUAST is..."
    }
    print(json.dumps(new_response, indent=2))
    
    print("\nKEY CHANGES:")
    print("✅ Request: Removed top_k and temperature (now hardcoded)")
    print("✅ Response: Only returns the response text")
    print("✅ Simplified: Easier to integrate and use")
    print("✅ Error handling: Built into the response text")


def main():
    """Main demo function."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Demo the SKUAST RAG API")
    parser.add_argument("--url", default="http://localhost:8000", 
                       help="Base URL of the API server")
    parser.add_argument("--test-errors", action="store_true",
                       help="Test error scenarios")
    parser.add_argument("--show-schema", action="store_true",
                       help="Show schema comparison")
    
    args = parser.parse_args()
    
    if args.show_schema:
        compare_old_vs_new_schema()
        return
    
    # Check if server is running
    try:
        response = requests.get(f"{args.url}/docs", timeout=5)
        if response.status_code != 200:
            print(f"❌ Server not accessible at {args.url}")
            print("Make sure the server is running: python main.py")
            return
    except requests.exceptions.RequestException:
        print(f"❌ Cannot connect to server at {args.url}")
        print("Make sure the server is running: python main.py")
        return
    
    # Run tests
    test_api_endpoint(args.url)
    
    if args.test_errors:
        test_error_scenarios(args.url)


if __name__ == "__main__":
    main()
