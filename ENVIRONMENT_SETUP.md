# Environment Setup Guide

## Required Environment Variables

The SKUAST RAG system requires the following environment variables to be configured:

### Google API Key (Required)
```bash
GOOGLE_API_KEY=your_google_api_key_here
```

**How to obtain:**
1. Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
2. Create a new API key or use an existing one
3. Copy the API key and set it as an environment variable

**Usage:**
- Used for Google's text-embedding-004 model for vector embeddings
- Used for Gemini LLM for response generation

### Setting Environment Variables

#### Option 1: .env file (Recommended for development)
Create a `.env` file in the project root directory:
```bash
# .env file
GOOGLE_API_KEY=your_google_api_key_here
```

#### Option 2: System environment variables
```bash
# Linux/macOS
export GOOGLE_API_KEY=your_google_api_key_here

# Windows
set GOOGLE_API_KEY=your_google_api_key_here
```

#### Option 3: Docker environment
```dockerfile
ENV GOOGLE_API_KEY=your_google_api_key_here
```

## Configuration Changes

### Vector Embeddings
- **Previous:** Used sentence-transformers with "all-MiniLM-L6-v2" model (384 dimensions)
- **Current:** Uses Google's "text-embedding-004" model (768 dimensions)

### API Schema Changes
- **Request:** Simplified to only accept `{"query": "string"}`
- **Response:** Simplified to only return `{"response": "string"}`
- **Parameters:** `top_k=3` and `temperature=0.3` are now hardcoded in the backend

### Error Handling
- Comprehensive error handling for all API calls
- User-friendly error messages
- Proper HTTP status codes
- Automatic detection and replacement of "missing information" responses

## Migration Notes

### From Sentence Transformers to Google Embeddings
If you have existing vector indices created with sentence transformers, they will need to be rebuilt with Google embeddings due to the different embedding dimensions (384 vs 768).

The system will automatically detect incompatible indices and rebuild them when the PDF is processed.

### API Compatibility
The API endpoints remain the same, but the request/response schemas have been simplified:

**Before:**
```json
// Request
{
  "query": "What is SKUAST?",
  "top_k": 5,
  "temperature": 0.7
}

// Response
{
  "response": "SKUAST is...",
  "success": true,
  "sources": [...],
  "metadata": {...},
  "error": null
}
```

**After:**
```json
// Request
{
  "query": "What is SKUAST?"
}

// Response
{
  "response": "SKUAST is..."
}
```

## Troubleshooting

### Common Issues

1. **"Google API key not configured" error**
   - Ensure GOOGLE_API_KEY environment variable is set
   - Verify the API key is valid and has the necessary permissions

2. **"Failed to validate Google API key" error**
   - Check if the API key has access to Gemini and Embedding APIs
   - Verify the API key hasn't expired

3. **"Vector database initialization failed" error**
   - Check if the Google API key is properly configured
   - Ensure network connectivity to Google's APIs

4. **Index rebuild required**
   - This is normal when migrating from sentence transformers
   - The system will automatically rebuild the index with Google embeddings

### API Rate Limits
- Google's embedding API has rate limits (1500 requests/minute by default)
- The system includes automatic rate limiting and retry logic
- Large document processing may take longer due to rate limiting

## Performance Considerations

### Embedding Generation
- Google embeddings may be slower than local sentence transformers
- Network latency affects embedding generation speed
- Batch processing is used to optimize API calls

### Cost Considerations
- Google's embedding API has usage-based pricing
- Monitor your API usage in Google Cloud Console
- Consider caching embeddings for frequently accessed content
