#!/usr/bin/env python3
"""
Manual validation script for the modernized RAG system.
Run this script to validate that all components are working correctly.
"""

import os
import sys
import asyncio
import requests
import json
from pathlib import Path

# Add src to path
sys.path.append('src')

from config import Config
from vector_db import VectorDatabase


def check_environment():
    """Check if required environment variables are set."""
    print("🔍 Checking environment variables...")
    
    if not os.getenv('GOOGLE_API_KEY'):
        print("❌ GOOGLE_API_KEY not found in environment variables")
        print("   Please set your Google API key:")
        print("   export GOOGLE_API_KEY=your_api_key_here")
        return False
    else:
        print("✅ GOOGLE_API_KEY is set")
        return True


def check_dependencies():
    """Check if required dependencies are installed."""
    print("\n🔍 Checking dependencies...")
    
    required_packages = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'google.generativeai',
        'faiss',
        'numpy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            if package == 'google.generativeai':
                import google.generativeai
            elif package == 'faiss':
                import faiss
            else:
                __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("   Install with: pip install -r requirements.txt")
        return False
    
    return True


def test_google_embeddings():
    """Test Google embeddings API."""
    print("\n🔍 Testing Google embeddings...")
    
    try:
        # Create a temporary vector database
        temp_db = VectorDatabase(
            model_name="models/text-embedding-004",
            db_path="temp_test_db"
        )
        
        # Test embedding creation
        test_texts = ["This is a test sentence.", "Another test sentence."]
        embeddings = temp_db.create_embeddings(test_texts)
        
        if embeddings.shape == (2, 768):
            print("✅ Google embeddings working correctly")
            print(f"   Created embeddings with shape: {embeddings.shape}")
            
            # Clean up
            import shutil
            if Path("temp_test_db").exists():
                shutil.rmtree("temp_test_db")
            
            return True
        else:
            print(f"❌ Unexpected embedding shape: {embeddings.shape}")
            return False
            
    except Exception as e:
        print(f"❌ Google embeddings test failed: {e}")
        return False


def test_api_endpoints():
    """Test API endpoints."""
    print("\n🔍 Testing API endpoints...")
    
    # Start the server in background (this is a simplified test)
    # In practice, you'd run the server separately
    
    try:
        # Test if server is running
        response = requests.get("http://localhost:8000/docs", timeout=5)
        if response.status_code == 200:
            print("✅ FastAPI server is running")
            
            # Test chat endpoint
            chat_response = requests.post(
                "http://localhost:8000/chat",
                json={"query": "What is SKUAST?"},
                timeout=30
            )
            
            if chat_response.status_code == 200:
                data = chat_response.json()
                if 'response' in data and len(data) == 1:
                    print("✅ Chat endpoint working with simplified schema")
                    print(f"   Response: {data['response'][:100]}...")
                    return True
                else:
                    print(f"❌ Unexpected response schema: {list(data.keys())}")
                    return False
            else:
                print(f"❌ Chat endpoint failed: {chat_response.status_code}")
                return False
        else:
            print("❌ FastAPI server not accessible")
            print("   Start the server with: python main.py")
            return False
            
    except requests.exceptions.RequestException as e:
        print("❌ Cannot connect to FastAPI server")
        print("   Make sure the server is running: python main.py")
        return False


def test_missing_info_detection():
    """Test missing information detection."""
    print("\n🔍 Testing missing information detection...")
    
    try:
        # Import the function
        from main import detect_missing_information_response, get_friendly_fallback_response
        
        # Test positive cases
        positive_cases = [
            "The document does not contain information about this.",
            "No information about this topic is available.",
            "This is not mentioned in the document."
        ]
        
        for case in positive_cases:
            if not detect_missing_information_response(case):
                print(f"❌ Failed to detect missing info in: {case}")
                return False
        
        # Test negative cases
        negative_cases = [
            "SKUAST is a university in India.",
            "The university offers agricultural programs.",
            "Here is the information you requested."
        ]
        
        for case in negative_cases:
            if detect_missing_information_response(case):
                print(f"❌ False positive for: {case}")
                return False
        
        # Test fallback response
        fallback = get_friendly_fallback_response()
        if not fallback or len(fallback) < 10:
            print("❌ Invalid fallback response")
            return False
        
        print("✅ Missing information detection working correctly")
        print(f"   Sample fallback: {fallback}")
        return True
        
    except Exception as e:
        print(f"❌ Missing info detection test failed: {e}")
        return False


def test_configuration():
    """Test configuration settings."""
    print("\n🔍 Testing configuration...")
    
    try:
        # Check that Google embeddings are default
        if Config.EMBEDDING_MODEL != "models/text-embedding-004":
            print(f"❌ Expected Google embedding model, got: {Config.EMBEDDING_MODEL}")
            return False
        
        if Config.EMBEDDING_DIMENSION != 768:
            print(f"❌ Expected 768 dimensions, got: {Config.EMBEDDING_DIMENSION}")
            return False
        
        # Check legacy config exists
        if not hasattr(Config, 'LEGACY_EMBEDDING_MODEL'):
            print("❌ Legacy embedding model config missing")
            return False
        
        print("✅ Configuration is correct")
        print(f"   Embedding model: {Config.EMBEDDING_MODEL}")
        print(f"   Embedding dimension: {Config.EMBEDDING_DIMENSION}")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """Run all validation tests."""
    print("🚀 SKUAST RAG System Validation")
    print("=" * 50)
    
    tests = [
        ("Environment Variables", check_environment),
        ("Dependencies", check_dependencies),
        ("Configuration", test_configuration),
        ("Google Embeddings", test_google_embeddings),
        ("Missing Info Detection", test_missing_info_detection),
        ("API Endpoints", test_api_endpoints),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 VALIDATION SUMMARY")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The system is ready to use.")
        return 0
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
