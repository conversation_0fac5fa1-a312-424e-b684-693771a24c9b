"""
Semantic search and retrieval module.
"""

import re
import logging
from typing import List, Dict, Any, Optional
try:
    from .vector_db import VectorDatabase
except ImportError:
    from vector_db import VectorDatabase

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SemanticRetriever:
    """
    A class to handle semantic search and retrieval operations.
    """
    
    def __init__(self, vector_db: VectorDatabase, min_score_threshold: float = 0.1):
        """
        Initialize the semantic retriever.
        
        Args:
            vector_db (VectorDatabase): Vector database instance
            min_score_threshold (float): Minimum similarity score threshold
        """
        self.vector_db = vector_db
        self.min_score_threshold = min_score_threshold
        
        logger.info(f"SemanticRetriever initialized with threshold: {min_score_threshold}")
    
    def preprocess_query(self, query: str) -> str:
        """
        Preprocess the user query for better search results.
        
        Args:
            query (str): Raw user query
            
        Returns:
            str: Preprocessed query
        """
        if not query:
            return ""
        
        # Remove extra whitespace
        query = re.sub(r'\s+', ' ', query.strip())
        
        # Expand common abbreviations
        abbreviations = {
            'SKUAST': 'Sher-e-Kashmir University of Agricultural Sciences and Technology',
            'agri': 'agriculture agricultural',
            'univ': 'university',
            'dept': 'department',
            'prog': 'program programme',
            'res': 'research',
            'dev': 'development',
            'tech': 'technology',
            'sci': 'science sciences'
        }
        
        for abbrev, expansion in abbreviations.items():
            query = re.sub(rf'\b{re.escape(abbrev)}\b', expansion, query, flags=re.IGNORECASE)
        
        # Add related terms for better matching
        query_terms = query.lower().split()
        
        # Add context-specific terms
        if any(term in query_terms for term in ['course', 'program', 'degree', 'study']):
            query += ' education academic curriculum'
        
        if any(term in query_terms for term in ['research', 'project', 'study']):
            query += ' investigation analysis experiment'
        
        if any(term in query_terms for term in ['agriculture', 'farming', 'crop']):
            query += ' agricultural cultivation production'
        
        return query.strip()
    
    def retrieve_relevant_chunks(self, query: str, top_k: int = 3, 
                                include_metadata: bool = True) -> List[Dict[str, Any]]:
        """
        Retrieve the most relevant chunks for a given query.
        
        Args:
            query (str): User query
            top_k (int): Number of top results to return
            include_metadata (bool): Whether to include chunk metadata
            
        Returns:
            List[Dict[str, Any]]: List of relevant chunks with scores
        """
        if not query.strip():
            logger.warning("Empty query provided")
            return []
        
        # Preprocess query
        processed_query = self.preprocess_query(query)
        logger.info(f"Original query: '{query}'")
        logger.info(f"Processed query: '{processed_query}'")
        
        # Search using vector database
        results = self.vector_db.search(processed_query, top_k=top_k)
        
        # Filter by score threshold
        filtered_results = [
            result for result in results 
            if result['score'] >= self.min_score_threshold
        ]
        
        if len(filtered_results) < len(results):
            logger.info(f"Filtered {len(results) - len(filtered_results)} results below threshold")
        
        # Enhance results with additional information
        enhanced_results = []
        for result in filtered_results:
            enhanced_result = {
                'chunk_id': result['chunk_id'],
                'text': result['text'],
                'relevance_score': result['score'],
                'rank': result['rank']
            }
            
            if include_metadata:
                enhanced_result['metadata'] = result['metadata']
                enhanced_result['text_preview'] = self._create_text_preview(result['text'])
                enhanced_result['query_terms_found'] = self._find_query_terms(query, result['text'])
            
            enhanced_results.append(enhanced_result)
        
        logger.info(f"Retrieved {len(enhanced_results)} relevant chunks")
        return enhanced_results
    
    def create_context_for_llm(self, query: str, top_k: int = 3) -> Dict[str, Any]:
        """
        Create formatted context for LLM based on retrieved chunks.
        
        Args:
            query (str): User query
            top_k (int): Number of chunks to retrieve
            
        Returns:
            Dict[str, Any]: Formatted context for LLM
        """
        # Retrieve relevant chunks
        chunks = self.retrieve_relevant_chunks(query, top_k=top_k)
        
        if not chunks:
            return {
                'context': "No relevant information found in the SKUAST document.",
                'sources': [],
                'query': query,
                'num_sources': 0
            }
        
        # Format context text
        context_parts = []
        sources = []
        
        for i, chunk in enumerate(chunks, 1):
            # Add chunk with source reference
            context_parts.append(f"[Source {i}]\n{chunk['text']}")
            
            sources.append({
                'source_id': i,
                'chunk_id': chunk['chunk_id'],
                'relevance_score': chunk['relevance_score'],
                'text_preview': chunk.get('text_preview', chunk['text'][:100] + '...')
            })
        
        context_text = "\n\n".join(context_parts)
        
        return {
            'context': context_text,
            'sources': sources,
            'query': query,
            'num_sources': len(chunks),
            'avg_relevance_score': sum(chunk['relevance_score'] for chunk in chunks) / len(chunks)
        }
    
    def _create_text_preview(self, text: str, max_length: int = 150) -> str:
        """
        Create a preview of the text.
        
        Args:
            text (str): Full text
            max_length (int): Maximum length of preview
            
        Returns:
            str: Text preview
        """
        if len(text) <= max_length:
            return text
        
        # Try to cut at sentence boundary
        preview = text[:max_length]
        last_sentence_end = max(
            preview.rfind('.'),
            preview.rfind('!'),
            preview.rfind('?')
        )
        
        if last_sentence_end > max_length * 0.7:  # If we can cut at a reasonable sentence boundary
            return preview[:last_sentence_end + 1]
        else:
            # Cut at word boundary
            last_space = preview.rfind(' ')
            if last_space > max_length * 0.8:
                return preview[:last_space] + '...'
            else:
                return preview + '...'
    
    def _find_query_terms(self, query: str, text: str) -> List[str]:
        """
        Find query terms that appear in the text.
        
        Args:
            query (str): Original query
            text (str): Text to search in
            
        Returns:
            List[str]: List of found terms
        """
        query_words = set(re.findall(r'\b\w+\b', query.lower()))
        text_words = set(re.findall(r'\b\w+\b', text.lower()))
        
        # Find common words (excluding very common words)
        common_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        
        found_terms = []
        for word in query_words:
            if word in text_words and word not in common_words and len(word) > 2:
                found_terms.append(word)
        
        return found_terms
    
    def get_retrieval_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the retrieval system.
        
        Returns:
            Dict[str, Any]: Retrieval statistics
        """
        vector_stats = self.vector_db.get_index_stats()
        
        retrieval_stats = {
            'retriever_status': 'Ready' if self.vector_db.index is not None else 'Not Ready',
            'min_score_threshold': self.min_score_threshold,
            'vector_db_stats': vector_stats
        }
        
        return retrieval_stats
    
    def update_score_threshold(self, new_threshold: float) -> None:
        """
        Update the minimum score threshold.
        
        Args:
            new_threshold (float): New threshold value
        """
        old_threshold = self.min_score_threshold
        self.min_score_threshold = new_threshold
        logger.info(f"Score threshold updated from {old_threshold} to {new_threshold}")
    
    def search_with_filters(self, query: str, top_k: int = 3, 
                           min_length: Optional[int] = None,
                           max_length: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Search with additional filters on chunk properties.
        
        Args:
            query (str): User query
            top_k (int): Number of results to return
            min_length (int, optional): Minimum chunk length
            max_length (int, optional): Maximum chunk length
            
        Returns:
            List[Dict[str, Any]]: Filtered search results
        """
        # Get initial results
        results = self.retrieve_relevant_chunks(query, top_k=top_k * 2)  # Get more to allow for filtering
        
        # Apply filters
        filtered_results = []
        for result in results:
            chunk_length = result['metadata']['length']
            
            # Apply length filters
            if min_length is not None and chunk_length < min_length:
                continue
            if max_length is not None and chunk_length > max_length:
                continue
            
            filtered_results.append(result)
            
            # Stop when we have enough results
            if len(filtered_results) >= top_k:
                break
        
        logger.info(f"Applied filters: min_length={min_length}, max_length={max_length}")
        logger.info(f"Filtered results: {len(filtered_results)} out of {len(results)}")
        
        return filtered_results


def main():
    """
    Main function for testing the semantic retriever.
    """
    # This would normally use a real vector database
    # For testing, we'll create a mock setup
    
    from .vector_db import VectorDatabase
    
    # Initialize components
    vector_db = VectorDatabase()
    retriever = SemanticRetriever(vector_db)
    
    # Sample chunks for testing
    sample_chunks = [
        {
            'id': 'chunk_1',
            'text': 'SKUAST offers undergraduate and postgraduate programs in agricultural sciences, including agronomy, horticulture, and plant pathology.',
            'length': 125,
            'chunk_index': 0,
            'token_count': 20
        },
        {
            'id': 'chunk_2',
            'text': 'The university conducts research in crop improvement, soil science, and sustainable farming practices to benefit farmers in Kashmir.',
            'length': 120,
            'chunk_index': 1,
            'token_count': 19
        }
    ]
    
    # Build index
    vector_db.build_index(sample_chunks)
    
    # Test retrieval
    test_query = "What programs does SKUAST offer?"
    
    print(f"Query: {test_query}")
    print("\nRetrieved chunks:")
    
    chunks = retriever.retrieve_relevant_chunks(test_query, top_k=2)
    for chunk in chunks:
        print(f"- Score: {chunk['relevance_score']:.4f}")
        print(f"  Text: {chunk['text'][:100]}...")
        print()
    
    # Test context creation
    context = retriever.create_context_for_llm(test_query)
    print("LLM Context:")
    print(context['context'][:200] + "...")
    
    # Show stats
    stats = retriever.get_retrieval_stats()
    print(f"\nRetrieval Stats: {stats}")


if __name__ == "__main__":
    main()
