"""
FAISS vector database module for semantic search.
"""

import os
import pickle
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
import faiss
from sentence_transformers import SentenceTransformer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class VectorDatabase:
    """
    A class to handle FAISS vector database operations for semantic search.
    """
    
    def __init__(self, model_name: str = "all-MiniLM-L6-v2", db_path: str = "data/vector_db"):
        """
        Initialize the vector database.
        
        Args:
            model_name (str): Name of the sentence transformer model
            db_path (str): Path to store the vector database
        """
        self.model_name = model_name
        self.db_path = Path(db_path)
        self.db_path.mkdir(parents=True, exist_ok=True)
        
        # Initialize sentence transformer model
        logger.info(f"Loading sentence transformer model: {model_name}")
        self.model = SentenceTransformer(model_name)
        self.embedding_dim = self.model.get_sentence_embedding_dimension()
        
        # Initialize FAISS index
        self.index = None
        self.chunks_metadata = []
        
        logger.info(f"VectorDatabase initialized with embedding dimension: {self.embedding_dim}")
    
    def create_embeddings(self, texts: List[str], batch_size: int = 32) -> np.ndarray:
        """
        Create embeddings for a list of texts.
        
        Args:
            texts (List[str]): List of text strings
            batch_size (int): Batch size for processing
            
        Returns:
            np.ndarray: Array of embeddings
        """
        if not texts:
            return np.array([])
        
        logger.info(f"Creating embeddings for {len(texts)} texts")
        
        try:
            # Create embeddings in batches
            embeddings = self.model.encode(
                texts,
                batch_size=batch_size,
                show_progress_bar=True,
                convert_to_numpy=True,
                normalize_embeddings=True  # Normalize for cosine similarity
            )
            
            logger.info(f"Successfully created embeddings with shape: {embeddings.shape}")
            return embeddings
            
        except Exception as e:
            logger.error(f"Error creating embeddings: {e}")
            raise
    
    def build_index(self, chunks: List[Dict[str, Any]], status_callback=None) -> bool:
        """
        Build FAISS index from text chunks.

        Args:
            chunks (List[Dict[str, Any]]): List of chunk dictionaries
            status_callback: Optional callback function for status updates

        Returns:
            bool: True if successful, False otherwise
        """
        if not chunks:
            msg = "No chunks provided for index building"
            logger.warning(msg)
            if status_callback:
                status_callback(msg, "warning")
            return False

        msg = f"Building FAISS index for {len(chunks)} chunks"
        logger.info(msg)
        if status_callback:
            status_callback(msg, "info")

        # Extract texts from chunks and validate
        valid_chunks = []
        texts = []

        for i, chunk in enumerate(chunks):
            if not chunk.get('text') or not chunk['text'].strip():
                warn_msg = f"Skipping chunk {i} due to empty text content"
                logger.warning(warn_msg)
                if status_callback:
                    status_callback(warn_msg, "warning")
                continue

            valid_chunks.append(chunk)
            texts.append(chunk['text'])

        if not texts:
            msg = "No valid text content found in chunks"
            logger.error(msg)
            if status_callback:
                status_callback(msg, "error")
            return False

        # Create embeddings
        try:
            embeddings = self.create_embeddings(texts)

            if embeddings.size == 0:
                msg = "No embeddings created - all texts failed to embed"
                logger.error(msg)
                if status_callback:
                    status_callback(msg, "error")
                return False

            # Validate embeddings shape
            if embeddings.ndim != 2 or embeddings.shape[1] != self.embedding_dim:
                msg = f"Embeddings shape error. Expected (N, {self.embedding_dim}), got {embeddings.shape}"
                logger.error(msg)
                if status_callback:
                    status_callback(msg, "error")
                return False

            # Create FAISS index
            # Using IndexFlatIP for cosine similarity (since embeddings are normalized)
            self.index = faiss.IndexFlatIP(self.embedding_dim)

            # Add embeddings to index
            self.index.add(embeddings.astype(np.float32))

            # Store metadata for valid chunks only
            self.chunks_metadata = valid_chunks.copy()

            success_msg = f"FAISS index built successfully with {self.index.ntotal} vectors"
            logger.info(success_msg)
            if status_callback:
                status_callback(success_msg, "success")

            return True

        except Exception as e:
            error_msg = f"Error building FAISS index: {e}"
            logger.error(error_msg)
            if status_callback:
                status_callback(error_msg, "error")
            return False
    
    def save_index(self, index_name: str = "skuast_index") -> None:
        """
        Save the FAISS index and metadata to disk.
        
        Args:
            index_name (str): Name for the index files
        """
        if self.index is None:
            logger.error("No index to save")
            return
        
        try:
            # Save FAISS index
            index_path = self.db_path / f"{index_name}.faiss"
            faiss.write_index(self.index, str(index_path))
            
            # Save metadata
            metadata_path = self.db_path / f"{index_name}_metadata.pkl"
            with open(metadata_path, 'wb') as f:
                pickle.dump(self.chunks_metadata, f)
            
            # Save model info
            model_info = {
                'model_name': self.model_name,
                'embedding_dim': self.embedding_dim,
                'num_vectors': self.index.ntotal
            }
            info_path = self.db_path / f"{index_name}_info.pkl"
            with open(info_path, 'wb') as f:
                pickle.dump(model_info, f)
            
            logger.info(f"Index saved successfully to {self.db_path}")
            
        except Exception as e:
            logger.error(f"Error saving index: {e}")
            raise
    
    def load_index(self, index_name: str = "skuast_index") -> bool:
        """
        Load the FAISS index and metadata from disk.
        
        Args:
            index_name (str): Name of the index files
            
        Returns:
            bool: True if loaded successfully, False otherwise
        """
        try:
            # Check if files exist
            index_path = self.db_path / f"{index_name}.faiss"
            metadata_path = self.db_path / f"{index_name}_metadata.pkl"
            info_path = self.db_path / f"{index_name}_info.pkl"
            
            if not all(path.exists() for path in [index_path, metadata_path, info_path]):
                logger.warning(f"Index files not found in {self.db_path}")
                return False
            
            # Load model info and verify compatibility
            with open(info_path, 'rb') as f:
                model_info = pickle.load(f)
            
            if model_info['model_name'] != self.model_name:
                logger.warning(f"Model mismatch: expected {self.model_name}, found {model_info['model_name']}")
                return False
            
            # Load FAISS index
            self.index = faiss.read_index(str(index_path))
            
            # Load metadata
            with open(metadata_path, 'rb') as f:
                self.chunks_metadata = pickle.load(f)
            
            logger.info(f"Index loaded successfully: {self.index.ntotal} vectors")
            return True
            
        except Exception as e:
            logger.error(f"Error loading index: {e}")
            return False
    
    def search(self, query: str, top_k: int = 3) -> List[Dict[str, Any]]:
        """
        Search for similar chunks using the query.

        Args:
            query (str): Search query
            top_k (int): Number of top results to return

        Returns:
            List[Dict[str, Any]]: List of search results with scores
        """
        if self.index is None or self.index.ntotal == 0:
            logger.error("No index loaded or index is empty")
            return []

        if not query.strip():
            logger.warning("Empty query provided")
            return []

        try:
            # Create embedding for query
            query_embedding = self.model.encode([query], normalize_embeddings=True)

            if query_embedding.size == 0:
                logger.error("Failed to create query embedding")
                return []

            # Adjust top_k to available vectors
            actual_k = min(top_k, self.index.ntotal)
            if actual_k == 0:
                logger.warning("No vectors available for search")
                return []

            # Search in FAISS index
            scores, indices = self.index.search(query_embedding.astype(np.float32), actual_k)

            # Validate search results
            if indices.size == 0 or (indices.ndim > 1 and len(indices[0]) > 0 and indices[0][0] == -1):
                logger.info("No relevant documents found")
                return []

            # Prepare results with bounds checking
            results = []
            for i, (score, idx) in enumerate(zip(scores[0], indices[0])):
                if idx == -1:  # Invalid index
                    continue

                if 0 <= idx < len(self.chunks_metadata):
                    result = {
                        'rank': i + 1,
                        'score': float(score),
                        'chunk_id': self.chunks_metadata[idx]['id'],
                        'text': self.chunks_metadata[idx]['text'],
                        'metadata': {
                            'length': self.chunks_metadata[idx]['length'],
                            'chunk_index': self.chunks_metadata[idx]['chunk_index'],
                            'token_count': self.chunks_metadata[idx].get('token_count', 0)
                        }
                    }
                    results.append(result)
                else:
                    logger.warning(f"Index {idx} from FAISS search is out of bounds for chunks_metadata (size {len(self.chunks_metadata)})")

            logger.info(f"Search completed: found {len(results)} results for query: '{query[:50]}...'")
            return results

        except Exception as e:
            logger.error(f"Error during search: {e}")
            return []
    
    def get_index_stats(self) -> Dict[str, Any]:
        """
        Get statistics about the current index.
        
        Returns:
            Dict[str, Any]: Index statistics
        """
        if self.index is None:
            return {'status': 'No index loaded'}
        
        stats = {
            'status': 'Index loaded',
            'model_name': self.model_name,
            'embedding_dimension': self.embedding_dim,
            'total_vectors': self.index.ntotal,
            'total_chunks': len(self.chunks_metadata),
            'index_type': type(self.index).__name__
        }
        
        if self.chunks_metadata:
            total_chars = sum(chunk['length'] for chunk in self.chunks_metadata)
            total_tokens = sum(chunk.get('token_count', 0) for chunk in self.chunks_metadata)
            
            stats.update({
                'total_characters': total_chars,
                'total_tokens': total_tokens,
                'avg_chunk_length': total_chars / len(self.chunks_metadata),
                'avg_tokens_per_chunk': total_tokens / len(self.chunks_metadata) if total_tokens > 0 else 0
            })
        
        return stats
    
    def add_chunks(self, new_chunks: List[Dict[str, Any]]) -> None:
        """
        Add new chunks to existing index.
        
        Args:
            new_chunks (List[Dict[str, Any]]): List of new chunk dictionaries
        """
        if not new_chunks:
            logger.warning("No new chunks to add")
            return
        
        logger.info(f"Adding {len(new_chunks)} new chunks to index")
        
        # Create embeddings for new chunks
        texts = [chunk['text'] for chunk in new_chunks]
        embeddings = self.create_embeddings(texts)
        
        if self.index is None:
            # Create new index if none exists
            self.index = faiss.IndexFlatIP(self.embedding_dim)
            self.chunks_metadata = []
        
        # Add to index
        self.index.add(embeddings.astype(np.float32))
        
        # Update metadata
        self.chunks_metadata.extend(new_chunks)
        
        logger.info(f"Successfully added chunks. Total vectors: {self.index.ntotal}")


def main():
    """
    Main function for testing the vector database.
    """
    # Initialize vector database
    vector_db = VectorDatabase()
    
    # Sample chunks for testing
    sample_chunks = [
        {
            'id': 'chunk_1',
            'text': 'This is about agriculture and farming techniques in Karnataka.',
            'length': 65,
            'chunk_index': 0,
            'token_count': 12
        },
        {
            'id': 'chunk_2', 
            'text': 'SKUAST focuses on agricultural research and education.',
            'length': 54,
            'chunk_index': 1,
            'token_count': 10
        },
        {
            'id': 'chunk_3',
            'text': 'The university offers various programs in agricultural sciences.',
            'length': 63,
            'chunk_index': 2,
            'token_count': 11
        }
    ]
    
    # Build index
    vector_db.build_index(sample_chunks)
    
    # Save index
    vector_db.save_index("test_index")
    
    # Test search
    results = vector_db.search("agricultural research", top_k=2)
    
    print("Search Results:")
    for result in results:
        print(f"Rank {result['rank']}: Score {result['score']:.4f}")
        print(f"Text: {result['text']}")
        print()
    
    # Show stats
    stats = vector_db.get_index_stats()
    print("Index Statistics:")
    for key, value in stats.items():
        print(f"{key}: {value}")


if __name__ == "__main__":
    main()
