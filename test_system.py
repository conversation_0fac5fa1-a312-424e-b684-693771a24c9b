#!/usr/bin/env python3
"""
Comprehensive test script for the SKUAST RAG system.
"""

import os
import sys
import time
import json
import requests
from pathlib import Path
from typing import Dict, Any, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from pdf_processor import PDFProcessor
from text_chunker import TextChunker
from vector_db import VectorDatabase
from gemini_client import GeminiClient

# Import retriever separately to handle the relative import issue
try:
    from retriever import SemanticRetriever
except ImportError:
    # Handle relative import issue
    import importlib.util
    spec = importlib.util.spec_from_file_location("retriever", Path(__file__).parent / "src" / "retriever.py")
    retriever_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(retriever_module)
    SemanticRetriever = retriever_module.SemanticRetriever


class SystemTester:
    """
    Comprehensive system testing class.
    """
    
    def __init__(self):
        self.test_results = {}
        self.api_base_url = "http://localhost:8000"
        
    def run_all_tests(self) -> Dict[str, Any]:
        """Run all system tests."""
        print("🚀 Starting SKUAST RAG System Tests")
        print("=" * 50)
        
        # Test individual components
        self.test_pdf_processor()
        self.test_text_chunker()
        self.test_vector_database()
        self.test_semantic_retriever()
        self.test_gemini_client()
        
        # Test API endpoints (if server is running)
        self.test_api_endpoints()
        
        # Generate test report
        self.generate_test_report()
        
        return self.test_results
    
    def test_pdf_processor(self):
        """Test PDF processing functionality."""
        print("\n📄 Testing PDF Processor...")
        
        try:
            processor = PDFProcessor()
            
            # Check if PDF exists
            pdf_path = "skuast.pdf"
            if not Path(pdf_path).exists():
                self.test_results['pdf_processor'] = {
                    'status': 'SKIPPED',
                    'reason': 'skuast.pdf not found',
                    'recommendation': 'Place skuast.pdf in the project root'
                }
                print("⚠️  PDF file not found - skipping PDF processor test")
                return
            
            # Test text extraction
            start_time = time.time()
            text_content = processor.extract_text_from_pdf(pdf_path)
            extraction_time = time.time() - start_time
            
            # Test text cleaning
            cleaned_text = processor.clean_text(text_content)
            
            # Test markdown conversion
            markdown_content = processor.convert_to_markdown(cleaned_text)
            
            self.test_results['pdf_processor'] = {
                'status': 'PASSED',
                'extraction_time': f"{extraction_time:.2f}s",
                'raw_text_length': len(text_content),
                'cleaned_text_length': len(cleaned_text),
                'markdown_length': len(markdown_content),
                'has_content': len(markdown_content) > 100
            }
            
            print(f"✅ PDF Processor: {len(text_content)} chars extracted in {extraction_time:.2f}s")
            
        except Exception as e:
            self.test_results['pdf_processor'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ PDF Processor failed: {e}")
    
    def test_text_chunker(self):
        """Test text chunking functionality."""
        print("\n📝 Testing Text Chunker...")
        
        try:
            chunker = TextChunker(chunk_size=1000, overlap_size=200)
            
            # Test with sample text
            sample_text = """
            # Introduction
            This is a test document for the SKUAST RAG system.
            
            ## Section 1
            This section contains information about agricultural programs.
            """ * 20  # Make it longer
            
            start_time = time.time()
            chunks = chunker.split_text_into_chunks(sample_text)
            chunking_time = time.time() - start_time
            
            # Get statistics
            stats = chunker.get_chunk_statistics(chunks)
            
            self.test_results['text_chunker'] = {
                'status': 'PASSED',
                'chunking_time': f"{chunking_time:.2f}s",
                'num_chunks': len(chunks),
                'avg_chunk_length': stats.get('avg_chunk_length', 0),
                'total_characters': stats.get('total_characters', 0),
                'overlap_preserved': chunker.overlap_size > 0
            }
            
            print(f"✅ Text Chunker: {len(chunks)} chunks created in {chunking_time:.2f}s")
            
        except Exception as e:
            self.test_results['text_chunker'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Text Chunker failed: {e}")
    
    def test_vector_database(self):
        """Test vector database functionality."""
        print("\n🔍 Testing Vector Database...")
        
        try:
            vector_db = VectorDatabase()
            
            # Test with sample chunks
            sample_chunks = [
                {
                    'id': 'test_chunk_1',
                    'text': 'SKUAST offers agricultural programs and research opportunities.',
                    'length': 60,
                    'chunk_index': 0,
                    'token_count': 10
                },
                {
                    'id': 'test_chunk_2',
                    'text': 'The university focuses on sustainable farming and crop improvement.',
                    'length': 65,
                    'chunk_index': 1,
                    'token_count': 11
                }
            ]
            
            # Test index building
            start_time = time.time()
            vector_db.build_index(sample_chunks)
            build_time = time.time() - start_time
            
            # Test search
            search_start = time.time()
            results = vector_db.search("agricultural programs", top_k=2)
            search_time = time.time() - search_start
            
            # Get stats
            stats = vector_db.get_index_stats()
            
            self.test_results['vector_database'] = {
                'status': 'PASSED',
                'build_time': f"{build_time:.2f}s",
                'search_time': f"{search_time:.3f}s",
                'index_stats': stats,
                'search_results': len(results),
                'embedding_dimension': vector_db.embedding_dim
            }
            
            print(f"✅ Vector Database: Index built in {build_time:.2f}s, search in {search_time:.3f}s")
            
        except Exception as e:
            self.test_results['vector_database'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Vector Database failed: {e}")
    
    def test_semantic_retriever(self):
        """Test semantic retrieval functionality."""
        print("\n🎯 Testing Semantic Retriever...")
        
        try:
            # Use the vector database from previous test
            vector_db = VectorDatabase()
            
            # Build a small index for testing
            sample_chunks = [
                {
                    'id': 'chunk_1',
                    'text': 'SKUAST offers undergraduate and graduate programs in agricultural sciences.',
                    'length': 70,
                    'chunk_index': 0,
                    'token_count': 12
                },
                {
                    'id': 'chunk_2',
                    'text': 'Research at SKUAST focuses on crop improvement and sustainable agriculture.',
                    'length': 75,
                    'chunk_index': 1,
                    'token_count': 13
                }
            ]
            
            vector_db.build_index(sample_chunks)
            retriever = SemanticRetriever(vector_db)
            
            # Test retrieval
            test_query = "What programs does SKUAST offer?"
            
            start_time = time.time()
            chunks = retriever.retrieve_relevant_chunks(test_query, top_k=2)
            retrieval_time = time.time() - start_time
            
            # Test context creation
            context = retriever.create_context_for_llm(test_query)
            
            self.test_results['semantic_retriever'] = {
                'status': 'PASSED',
                'retrieval_time': f"{retrieval_time:.3f}s",
                'chunks_retrieved': len(chunks),
                'context_created': len(context['context']) > 0,
                'avg_relevance_score': context.get('avg_relevance_score', 0)
            }
            
            print(f"✅ Semantic Retriever: {len(chunks)} chunks retrieved in {retrieval_time:.3f}s")
            
        except Exception as e:
            self.test_results['semantic_retriever'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Semantic Retriever failed: {e}")
    
    def test_gemini_client(self):
        """Test Gemini API client."""
        print("\n🤖 Testing Gemini Client...")
        
        try:
            # Check if API key is available
            api_key = os.getenv('GOOGLE_API_KEY')
            if not api_key:
                self.test_results['gemini_client'] = {
                    'status': 'SKIPPED',
                    'reason': 'GOOGLE_API_KEY not set',
                    'recommendation': 'Set GOOGLE_API_KEY environment variable'
                }
                print("⚠️  Google API key not found - skipping Gemini client test")
                return
            
            client = GeminiClient(api_key=api_key)
            
            # Test API key validation
            api_valid = client.validate_api_key()
            
            if not api_valid:
                self.test_results['gemini_client'] = {
                    'status': 'FAILED',
                    'reason': 'Invalid API key',
                    'recommendation': 'Check your Google API key'
                }
                print("❌ Gemini Client: Invalid API key")
                return
            
            # Test response generation
            test_query = "What is SKUAST?"
            test_context = "SKUAST is Sher-e-Kashmir University of Agricultural Sciences and Technology."
            
            start_time = time.time()
            response = client.generate_response(test_query, test_context)
            response_time = time.time() - start_time
            
            # Get model info
            model_info = client.get_model_info()
            
            self.test_results['gemini_client'] = {
                'status': 'PASSED',
                'api_key_valid': api_valid,
                'response_time': f"{response_time:.2f}s",
                'response_success': response['success'],
                'response_length': len(response.get('response', '')),
                'model_info': model_info
            }
            
            print(f"✅ Gemini Client: Response generated in {response_time:.2f}s")
            
        except Exception as e:
            self.test_results['gemini_client'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ Gemini Client failed: {e}")
    
    def test_api_endpoints(self):
        """Test API endpoints if server is running."""
        print("\n🌐 Testing API Endpoints...")
        
        try:
            # Test health endpoint
            health_response = requests.get(f"{self.api_base_url}/health", timeout=5)
            health_status = health_response.status_code == 200
            
            # Test status endpoint
            status_response = requests.get(f"{self.api_base_url}/status", timeout=5)
            status_status = status_response.status_code == 200
            
            # Test chat endpoint
            chat_payload = {
                "query": "What is SKUAST?",
                "top_k": 3,
                "temperature": 0.3
            }
            
            chat_response = requests.post(
                f"{self.api_base_url}/chat",
                json=chat_payload,
                timeout=30
            )
            chat_status = chat_response.status_code == 200
            
            self.test_results['api_endpoints'] = {
                'status': 'PASSED' if all([health_status, status_status, chat_status]) else 'PARTIAL',
                'health_endpoint': health_status,
                'status_endpoint': status_status,
                'chat_endpoint': chat_status,
                'server_running': True
            }
            
            if chat_status:
                chat_data = chat_response.json()
                self.test_results['api_endpoints']['chat_response_success'] = chat_data.get('success', False)
            
            print(f"✅ API Endpoints: Health={health_status}, Status={status_status}, Chat={chat_status}")
            
        except requests.exceptions.ConnectionError:
            self.test_results['api_endpoints'] = {
                'status': 'SKIPPED',
                'reason': 'Server not running',
                'recommendation': 'Start the server with: python main.py'
            }
            print("⚠️  Server not running - skipping API endpoint tests")
            
        except Exception as e:
            self.test_results['api_endpoints'] = {
                'status': 'FAILED',
                'error': str(e)
            }
            print(f"❌ API Endpoints failed: {e}")
    
    def generate_test_report(self):
        """Generate and display test report."""
        print("\n" + "=" * 50)
        print("📊 TEST REPORT")
        print("=" * 50)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASSED')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAILED')
        skipped_tests = sum(1 for result in self.test_results.values() if result['status'] == 'SKIPPED')
        
        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⚠️  Skipped: {skipped_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDetailed Results:")
        for component, result in self.test_results.items():
            status_emoji = {
                'PASSED': '✅',
                'FAILED': '❌',
                'SKIPPED': '⚠️',
                'PARTIAL': '🔶'
            }.get(result['status'], '❓')
            
            print(f"{status_emoji} {component.replace('_', ' ').title()}: {result['status']}")
            
            if result['status'] == 'FAILED' and 'error' in result:
                print(f"   Error: {result['error']}")
            elif result['status'] == 'SKIPPED' and 'reason' in result:
                print(f"   Reason: {result['reason']}")
                if 'recommendation' in result:
                    print(f"   Recommendation: {result['recommendation']}")
        
        # Save detailed results to file
        with open('test_results.json', 'w') as f:
            json.dump(self.test_results, f, indent=2, default=str)
        
        print(f"\nDetailed results saved to: test_results.json")
        
        # System readiness assessment
        print("\n🎯 SYSTEM READINESS ASSESSMENT")
        print("-" * 30)
        
        critical_components = ['pdf_processor', 'vector_database', 'gemini_client']
        critical_passed = sum(1 for comp in critical_components 
                            if self.test_results.get(comp, {}).get('status') == 'PASSED')
        
        if critical_passed == len(critical_components):
            print("🟢 System is READY for production use")
        elif critical_passed >= len(critical_components) - 1:
            print("🟡 System is MOSTLY READY (minor issues)")
        else:
            print("🔴 System is NOT READY (critical issues)")
        
        print("\nNext Steps:")
        if failed_tests > 0:
            print("1. Fix failed components before deployment")
        if skipped_tests > 0:
            print("2. Address skipped tests for full functionality")
        if self.test_results.get('api_endpoints', {}).get('status') == 'SKIPPED':
            print("3. Start the server and test API endpoints")
        
        print("\nTo start the system:")
        print("1. Ensure all dependencies are installed: pip3 install -r requirements.txt")
        print("2. Set your Google API key: export GOOGLE_API_KEY=your_key_here")
        print("3. Place skuast.pdf in the project root")
        print("4. Run the server: python3 main.py")


def main():
    """Main function to run all tests."""
    tester = SystemTester()
    results = tester.run_all_tests()
    
    # Exit with appropriate code
    failed_tests = sum(1 for result in results.values() if result['status'] == 'FAILED')
    sys.exit(1 if failed_tests > 0 else 0)


if __name__ == "__main__":
    main()
