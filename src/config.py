"""
Configuration module for SKUAST RAG system.
"""

import os
from typing import Dict, Any
from pathlib import Path


class Config:
    """
    Configuration class for SKUAST RAG system.
    """
    
    # API Configuration
    GOOGLE_API_KEY = os.getenv('GOOGLE_API_KEY')
    
    # Model Configuration
    EMBEDDING_MODEL = "all-MiniLM-L6-v2"
    EMBEDDING_DIMENSION = 384
    GEMINI_MODEL = "gemini-1.5-flash"
    
    # Alternative Google Embeddings (if needed)
    GOOGLE_EMBEDDING_MODEL = "models/text-embedding-004"
    GOOGLE_EMBEDDING_DIMENSION = 768
    
    # Text Processing Configuration
    CHUNK_SIZE = 5000
    OVERLAP_SIZE = 1000
    MAX_CONTEXT_LENGTH = 15000
    
    # Vector Database Configuration
    VECTOR_DB_PATH = "data/vector_db"
    INDEX_NAME = "skuast_index"
    
    # API Configuration
    API_HOST = "0.0.0.0"
    API_PORT = 8000
    API_RELOAD = True
    
    # Generation Configuration
    DEFAULT_TEMPERATURE = 0.3
    DEFAULT_MAX_TOKENS = 1000
    DEFAULT_TOP_K = 3
    MAX_TOP_K = 10
    
    # Error Handling Configuration
    MAX_RETRIES = 3
    RETRY_DELAY = 1.0
    
    # Logging Configuration
    LOG_LEVEL = "INFO"
    LOG_FILE = "logs/skuast_rag.log"
    
    # System Prompts
    SYSTEM_PROMPT = """You are SKUAST BOT, an AI assistant specialized in providing information about Sher-e-Kashmir University of Agricultural Sciences and Technology (SKUAST). 

Your role is to:
1. Answer questions based ONLY on the provided context from the SKUAST document
2. Provide accurate, helpful, and detailed responses about SKUAST
3. If the context doesn't contain enough information to answer a question, clearly state that the information is not available in the provided document
4. Always maintain a professional and helpful tone
5. When possible, provide specific details like program names, requirements, procedures, etc.

Important guidelines:
- NEVER make up information that is not in the provided context
- If you're unsure about something, say so clearly
- Always base your answers on the provided context
- If asked about topics not covered in the context, politely explain that the information is not available in the SKUAST document you have access to
- Be concise but comprehensive in your responses"""
    
    @classmethod
    def validate_config(cls) -> Dict[str, Any]:
        """
        Validate configuration and return status.
        
        Returns:
            Dict[str, Any]: Validation results
        """
        issues = []
        warnings = []
        
        # Check API key
        if not cls.GOOGLE_API_KEY:
            issues.append("GOOGLE_API_KEY environment variable not set")
        
        # Check paths
        vector_db_path = Path(cls.VECTOR_DB_PATH)
        if not vector_db_path.exists():
            warnings.append(f"Vector database path does not exist: {cls.VECTOR_DB_PATH}")
        
        # Check log directory
        log_path = Path(cls.LOG_FILE)
        if not log_path.parent.exists():
            warnings.append(f"Log directory does not exist: {log_path.parent}")
        
        # Check numeric values
        if cls.CHUNK_SIZE <= 0:
            issues.append("CHUNK_SIZE must be positive")
        
        if cls.OVERLAP_SIZE < 0:
            issues.append("OVERLAP_SIZE must be non-negative")
        
        if cls.OVERLAP_SIZE >= cls.CHUNK_SIZE:
            issues.append("OVERLAP_SIZE must be less than CHUNK_SIZE")
        
        if cls.DEFAULT_TOP_K <= 0 or cls.DEFAULT_TOP_K > cls.MAX_TOP_K:
            issues.append(f"DEFAULT_TOP_K must be between 1 and {cls.MAX_TOP_K}")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'warnings': warnings,
            'api_key_configured': bool(cls.GOOGLE_API_KEY)
        }
    
    @classmethod
    def get_chunking_config(cls) -> Dict[str, int]:
        """Get text chunking configuration."""
        return {
            'chunk_size': cls.CHUNK_SIZE,
            'overlap_size': cls.OVERLAP_SIZE
        }
    
    @classmethod
    def get_generation_config(cls) -> Dict[str, Any]:
        """Get generation configuration."""
        return {
            'temperature': cls.DEFAULT_TEMPERATURE,
            'max_tokens': cls.DEFAULT_MAX_TOKENS,
            'top_k': cls.DEFAULT_TOP_K,
            'max_context_length': cls.MAX_CONTEXT_LENGTH
        }
    
    @classmethod
    def get_vector_db_config(cls) -> Dict[str, Any]:
        """Get vector database configuration."""
        return {
            'model_name': cls.EMBEDDING_MODEL,
            'embedding_dimension': cls.EMBEDDING_DIMENSION,
            'db_path': cls.VECTOR_DB_PATH,
            'index_name': cls.INDEX_NAME
        }
    
    @classmethod
    def get_api_config(cls) -> Dict[str, Any]:
        """Get API configuration."""
        return {
            'host': cls.API_HOST,
            'port': cls.API_PORT,
            'reload': cls.API_RELOAD
        }
    
    @classmethod
    def create_directories(cls):
        """Create necessary directories."""
        # Create vector database directory
        Path(cls.VECTOR_DB_PATH).mkdir(parents=True, exist_ok=True)
        
        # Create log directory
        Path(cls.LOG_FILE).parent.mkdir(parents=True, exist_ok=True)
    
    @classmethod
    def get_environment_info(cls) -> Dict[str, Any]:
        """Get environment information."""
        return {
            'python_version': os.sys.version,
            'working_directory': os.getcwd(),
            'environment_variables': {
                'GOOGLE_API_KEY': 'SET' if cls.GOOGLE_API_KEY else 'NOT_SET',
                'PATH': os.getenv('PATH', 'NOT_SET')[:100] + '...' if os.getenv('PATH') else 'NOT_SET'
            }
        }


# Create directories on import
Config.create_directories()


def get_config_summary() -> str:
    """
    Get a formatted configuration summary.
    
    Returns:
        str: Formatted configuration summary
    """
    validation = Config.validate_config()
    
    summary = f"""
SKUAST RAG System Configuration
==============================

API Configuration:
- Google API Key: {'✓ Configured' if Config.GOOGLE_API_KEY else '✗ Not configured'}
- Gemini Model: {Config.GEMINI_MODEL}

Text Processing:
- Chunk Size: {Config.CHUNK_SIZE} characters
- Overlap Size: {Config.OVERLAP_SIZE} characters
- Max Context Length: {Config.MAX_CONTEXT_LENGTH} characters

Vector Database:
- Embedding Model: {Config.EMBEDDING_MODEL}
- Embedding Dimension: {Config.EMBEDDING_DIMENSION}
- Database Path: {Config.VECTOR_DB_PATH}

Generation Settings:
- Default Temperature: {Config.DEFAULT_TEMPERATURE}
- Default Max Tokens: {Config.DEFAULT_MAX_TOKENS}
- Default Top-K: {Config.DEFAULT_TOP_K}

Validation Status: {'✓ Valid' if validation['valid'] else '✗ Issues found'}
"""
    
    if validation['issues']:
        summary += f"\nIssues:\n"
        for issue in validation['issues']:
            summary += f"- {issue}\n"
    
    if validation['warnings']:
        summary += f"\nWarnings:\n"
        for warning in validation['warnings']:
            summary += f"- {warning}\n"
    
    return summary


if __name__ == "__main__":
    # Print configuration summary when run directly
    print(get_config_summary())
    
    # Validate configuration
    validation = Config.validate_config()
    if not validation['valid']:
        print("\n❌ Configuration validation failed!")
        exit(1)
    else:
        print("\n✅ Configuration validation passed!")
