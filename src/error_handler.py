"""
Comprehensive error handling and logging configuration.
"""

import logging
import traceback
from typing import Dict, Any, Optional
from datetime import datetime
from pathlib import Path


class ErrorHandler:
    """
    Centralized error handling and logging system.
    """
    
    def __init__(self, log_level: str = "INFO", log_file: Optional[str] = None):
        """
        Initialize error handler.
        
        Args:
            log_level (str): Logging level
            log_file (str, optional): Path to log file
        """
        self.log_level = getattr(logging, log_level.upper())
        self.log_file = log_file
        self.setup_logging()
        
        # Error categories
        self.error_categories = {
            'PDF_PROCESSING': 'PDF processing errors',
            'VECTOR_DB': 'Vector database errors',
            'RETRIEVAL': 'Semantic retrieval errors',
            'GEMINI_API': 'Google Gemini API errors',
            'SYSTEM': 'System initialization errors',
            'VALIDATION': 'Input validation errors',
            'NETWORK': 'Network and connectivity errors'
        }
    
    def setup_logging(self):
        """Set up logging configuration."""
        # Create logs directory if it doesn't exist
        if self.log_file:
            log_path = Path(self.log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Configure logging format
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        # Configure root logger
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # Remove existing handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # File handler if specified
        if self.log_file:
            file_handler = logging.FileHandler(self.log_file)
            file_handler.setLevel(self.log_level)
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
    
    def handle_error(self, error: Exception, category: str = 'SYSTEM', 
                    context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Handle and log errors with context.
        
        Args:
            error (Exception): The exception that occurred
            category (str): Error category
            context (dict, optional): Additional context information
            
        Returns:
            Dict[str, Any]: Error information dictionary
        """
        logger = logging.getLogger(__name__)
        
        # Create error info
        error_info = {
            'timestamp': datetime.now().isoformat(),
            'category': category,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'traceback': traceback.format_exc(),
            'context': context or {}
        }
        
        # Log the error
        logger.error(
            f"[{category}] {type(error).__name__}: {str(error)}\n"
            f"Context: {context}\n"
            f"Traceback: {traceback.format_exc()}"
        )
        
        return error_info
    
    def create_user_friendly_message(self, error_info: Dict[str, Any]) -> str:
        """
        Create user-friendly error message.
        
        Args:
            error_info (Dict[str, Any]): Error information
            
        Returns:
            str: User-friendly error message
        """
        category = error_info.get('category', 'SYSTEM')
        error_type = error_info.get('error_type', 'Unknown')
        
        # Category-specific messages
        if category == 'PDF_PROCESSING':
            return "There was an issue processing the PDF document. Please ensure the file is accessible and not corrupted."
        
        elif category == 'VECTOR_DB':
            return "There was an issue with the search database. The system may need to rebuild the search index."
        
        elif category == 'RETRIEVAL':
            return "Unable to find relevant information for your query. Please try rephrasing your question."
        
        elif category == 'GEMINI_API':
            if 'API key' in str(error_info.get('error_message', '')):
                return "There's an issue with the AI service configuration. Please check the API key settings."
            elif 'quota' in str(error_info.get('error_message', '')).lower():
                return "The AI service has reached its usage limit. Please try again later."
            else:
                return "The AI service is temporarily unavailable. Please try again in a moment."
        
        elif category == 'VALIDATION':
            return f"Invalid input: {error_info.get('error_message', 'Please check your input and try again.')}"
        
        elif category == 'NETWORK':
            return "Network connectivity issue. Please check your internet connection and try again."
        
        else:
            return "An unexpected error occurred. Please try again or contact support if the issue persists."
    
    def log_performance_metric(self, operation: str, duration: float, 
                              metadata: Optional[Dict[str, Any]] = None):
        """
        Log performance metrics.
        
        Args:
            operation (str): Operation name
            duration (float): Duration in seconds
            metadata (dict, optional): Additional metadata
        """
        logger = logging.getLogger(__name__)
        
        logger.info(
            f"PERFORMANCE - {operation}: {duration:.3f}s"
            f"{f' | {metadata}' if metadata else ''}"
        )
    
    def log_system_event(self, event: str, details: Optional[Dict[str, Any]] = None):
        """
        Log system events.
        
        Args:
            event (str): Event description
            details (dict, optional): Event details
        """
        logger = logging.getLogger(__name__)
        
        logger.info(
            f"SYSTEM_EVENT - {event}"
            f"{f' | {details}' if details else ''}"
        )


# Global error handler instance
error_handler = ErrorHandler(log_file="logs/skuast_rag.log")


def handle_pdf_processing_error(error: Exception, pdf_path: str = None) -> Dict[str, Any]:
    """Handle PDF processing specific errors."""
    context = {'pdf_path': pdf_path} if pdf_path else {}
    return error_handler.handle_error(error, 'PDF_PROCESSING', context)


def handle_vector_db_error(error: Exception, operation: str = None) -> Dict[str, Any]:
    """Handle vector database specific errors."""
    context = {'operation': operation} if operation else {}
    return error_handler.handle_error(error, 'VECTOR_DB', context)


def handle_retrieval_error(error: Exception, query: str = None) -> Dict[str, Any]:
    """Handle retrieval specific errors."""
    context = {'query': query} if query else {}
    return error_handler.handle_error(error, 'RETRIEVAL', context)


def handle_gemini_api_error(error: Exception, query: str = None, 
                           context_length: int = None) -> Dict[str, Any]:
    """Handle Gemini API specific errors."""
    context = {}
    if query:
        context['query'] = query[:100] + '...' if len(query) > 100 else query
    if context_length:
        context['context_length'] = context_length
    
    return error_handler.handle_error(error, 'GEMINI_API', context)


def handle_validation_error(error: Exception, field: str = None, 
                           value: Any = None) -> Dict[str, Any]:
    """Handle validation specific errors."""
    context = {}
    if field:
        context['field'] = field
    if value is not None:
        context['value'] = str(value)[:100]  # Limit value length
    
    return error_handler.handle_error(error, 'VALIDATION', context)


def handle_system_error(error: Exception, component: str = None) -> Dict[str, Any]:
    """Handle system initialization and general errors."""
    context = {'component': component} if component else {}
    return error_handler.handle_error(error, 'SYSTEM', context)


# Decorator for automatic error handling
def with_error_handling(category: str = 'SYSTEM'):
    """
    Decorator for automatic error handling.
    
    Args:
        category (str): Error category
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_info = error_handler.handle_error(e, category)
                # Re-raise the exception with additional context
                raise type(e)(f"{str(e)} [Error ID: {error_info['timestamp']}]") from e
        return wrapper
    return decorator


# Context manager for performance monitoring
class PerformanceMonitor:
    """Context manager for monitoring operation performance."""
    
    def __init__(self, operation: str, metadata: Optional[Dict[str, Any]] = None):
        self.operation = operation
        self.metadata = metadata
        self.start_time = None
    
    def __enter__(self):
        self.start_time = datetime.now()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = (datetime.now() - self.start_time).total_seconds()
            error_handler.log_performance_metric(
                self.operation, 
                duration, 
                self.metadata
            )


# Example usage functions
def log_api_request(endpoint: str, query: str, response_time: float, 
                   success: bool, error: str = None):
    """Log API request details."""
    logger = logging.getLogger(__name__)
    
    status = "SUCCESS" if success else "ERROR"
    log_message = f"API_REQUEST - {endpoint} | {status} | {response_time:.3f}s"
    
    if error:
        log_message += f" | Error: {error}"
    
    # Truncate query for logging
    query_preview = query[:100] + '...' if len(query) > 100 else query
    log_message += f" | Query: {query_preview}"
    
    if success:
        logger.info(log_message)
    else:
        logger.error(log_message)


def log_system_startup(components_status: Dict[str, bool]):
    """Log system startup information."""
    error_handler.log_system_event(
        "System Startup",
        {
            'components': components_status,
            'total_components': len(components_status),
            'successful_components': sum(components_status.values())
        }
    )
